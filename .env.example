PEXELS_API_KEY=
PIXABAY_API_KEY=
UNSPLASH_CLIENT_ID=
UNSPLASH_CLIENT_SECRET=
GETIMG_API_KEY=
ELEVEN_API_KEY=
OPENAI_API_KEY=
GIPHY_KEY=
TENOR_KEY=

GOOGLE_FONT_API_KEY=

# Google OAuth for YouTube API
GOOGLE_OAUTH_CLIENT_ID=
GOOGLE_OAUTH_CLIENT_SECRET=
GOOGLE_OAUTH_REDIRECT_URI=

# Encryption key for storing sensitive data (generate a random 32-character string)
ENCRYPTION_KEY=

POSTGRES_URL=
POSTGRES_USER=
POSTGRES_HOST=
SUPABASE_JWT_SECRET=
SUPABASE_ANON_KEY=
POSTGRES_PRISMA_URL=
POSTGRES_PASSWORD=""
POSTGRES_DATABASE=""
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_URL=
SUPABASE_SERVICE_ROLE_KEY=
POSTGRES_URL_NON_POOLING=


PODCAST_INDEX_API_KEY=
PODCAST_INDEX_API_SECRET=

INNGEST_API_BASE_URL="http://localhost:8288/v1"
INNGEST_SIGNING_KEY=
INNGEST_EVENT_KEY=local-dev-key


BROWSERLESS_IO_KEY=


REMOTION_AWS_ACCESS_KEY_ID=
REMOTION_AWS_SECRET_ACCESS_KEY=
REMOTION_DEPLOY_FUNCTION_NAME=
REMOTION_AWS_SITE_NAME=
REMOTION_AWS_SERVE_URL=
REMOTION_AWS_S3_BUCKET_NAME=
REMOTION_AWS_S3_PUBLIC_URL=


LEMONFOX_API_KEY=
