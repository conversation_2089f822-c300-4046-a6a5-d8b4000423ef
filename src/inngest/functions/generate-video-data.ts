import { inngest } from '../client'
import { db } from '@/lib/db'
import { projects } from '@/db/schema'
import { z } from 'zod'
import { zodResponseFormat } from 'openai/helpers/zod'
import type { InferInsertModel } from 'drizzle-orm'
import { generateLegacyCaptionsFromElevenLabs } from '@/lib/remotion/utils/captionUtils'
import fetch from 'node-fetch'
import fs from 'fs'
import OpenAI from 'openai'
import FormData from 'form-data'

import {
  extractBlogMarkdownAndImages,
  extractPDFMarkdownAndImages,
} from '../utils/content-extractor'
import { fetchMediaForScene } from '../utils/media-fetcher'
import ffprobe from 'ffprobe'
import ffprobeStatic from 'ffprobe-static'
import * as hash from 'object-hash'
import { createClient } from '@supabase/supabase-js'
import { incrementUsage } from '@/lib/usage-utils'
import {
  transcribeAudio,
  processWordsToScenes,
  TranscriptionService,
} from '../utils/transcription'

// Top-level supabase client for all helpers
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export const generateVideoData = inngest.createFunction(
  {
    id: 'generate-video-data',
    retries: 1, // Cap retries to only 1 attempt
  },
  { event: 'generate-video-data' },
  async ({ event }) => {
    // --- Step 0: Project metadata and constants ---
    console.log('Step 0: Setting up project metadata and constants')

    // Helper function to validate and normalize duration
    const validateDuration = (
      durationValue: number | undefined,
      fallback: number = 60
    ): number => {
      if (!durationValue || durationValue <= 0) {
        console.log(
          `Invalid duration ${durationValue}, using fallback: ${fallback}`
        )
        return fallback
      }

      // Cap duration at 24 hours (86,400 seconds) to prevent extreme values
      const maxDuration = 86400
      if (durationValue > maxDuration) {
        console.log(
          `Duration ${durationValue} exceeds maximum ${maxDuration}, capping to ${maxDuration}`
        )
        return maxDuration
      }

      return durationValue
    }

    const {
      idea,
      tone,
      audience,
      platform,
      hook,
      callToAction,
      keywords,
      duration,
      language,
      orientation,
      userId,
      organizationId,
    } = event?.data

    // Validate required fields
    if (!userId) {
      throw new Error('userId is required but was not provided')
    }

    // Project metadata
    const now = new Date().toISOString()
    let projectName = 'Untitled Project'
    const coverColor = '#FFE4E1'
    const coverPic = null

    // set constants - disable music for podcast flows
    const isPodcastOrAudioFlow =
      !!event?.data?.podcastUrl || !!event?.data?.audioUrl
    const music = {
      enabled: !isPodcastOrAudioFlow, // Disable music only for podcast flows
      src: 'https://storage.googleapis.com/dev_image_adorilabs/v1-stock-music/inspiring-upbeat-corporate-171604.mp3',
      volume: 30,
      duration: 115,
      name: 'Upbeat corporate',
    }

    let speech: {
      enabled: boolean
      src: string
      name: string
      volume: number
      transcript: {
        captions: {
          start: number
          end: number
          sentence: string
          wordBoundries: { start: number; end: number; word: string }[]
        }[]
        status: 'QUEUED' | 'COMPLETED' | 'FAILED'
      }
    } | null = null // only set speech for podcast and audio
    const captionSettings = {
      enabled: true,
      fontFamily: 'Inter',
      fontSize: 32,
      fontWeight: 'normal',
      fontStyle: 'normal',
      textColor: '#fff',
      highlightColor: '#3b82f6',
      backgroundColor: '#000',
      backgroundOpacity: 60,
      textAlign: 'center',
      textShadow: true,
      borderRadius: 8,
      padding: 16,
      maxWidth: 90,
      animation: 'fade',
    }
    const backgroundVideo = null // set for video editing with broll overlay
    // const backgroundVideo = {
    //   src: 'https://cdn.example.com/videos/delhi-background.mp4',
    //   muted: false,
    // }
    console.log('Step 0: Project metadata/constants set')

    // --- Step 1: Generate scenes using OpenAI Structured Output ---
    console.log('Step 1: Generating scenes with OpenAI')
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
    const ScriptSchema = z.object({
      scenes: z.array(
        z.object({
          sceneNumber: z.number(),
          text: z.string(),
          searchKeywords: z.string(),
        })
      ),
      summary: z.string(),
    })
    const systemPrompt = `You are an expert video script writer who creates engaging, platform-optimized scripts. You understand how to structure content for maximum engagement and always respond with valid JSON format.`

    let script: z.infer<typeof ScriptSchema> | null = null

    let userPrompt = ''
    let blog_images: string[] = []
    let pdf_images: string[] = []
    const method = event?.data?.method || 'Idea to Video'

    if (event?.data?.pdfUrl) {
      // --- PDF to Video flow ---
      try {
        const { markdown, pdf_images: imgs } =
          await extractPDFMarkdownAndImages(event.data.pdfUrl)
        pdf_images = imgs
        userPrompt = `Create a detailed video script based on the following PDF document content (in Markdown):\n\n${markdown}\n\n**Tone**: ${tone}\n**Target Audience**: ${audience}\n**Platform**: ${platform}\n**Duration**: ${duration} seconds\n**Language**: ${language}\n${keywords ? `**Keywords to include**: ${keywords}` : ''}\n${hook ? '**Include Hook**: Yes - Create an attention-grabbing opening' : ''}\n${callToAction ? '**Include Call-to-Action**: Yes - End with a clear next step' : ''}\n\n**Requirements**:\n1. Split the script into ${Math.ceil(duration / 10)} clearly defined scenes (approximately 8-12 seconds each)\n2. Each scene should have:\n   - Scene number\n   - Narration text (1-2 sentences, concise and engaging)\n   - Visual description keywords (what should be shown on screen)\n3. Make the script ${tone} in tone and appropriate for ${audience}\n4. Optimize for ${platform} format and audience expectations\n5. Keep the total duration around ${duration} seconds\n6. Make it visually engaging and easy to follow\n7. summary(summarize outline of script)`
      } catch (pdfError) {
        console.error('PDF extraction failed with error:', pdfError)

        // Check if it's a 400 error which likely means public access issue
        if (pdfError instanceof Error && pdfError.message.includes('400')) {
          throw new Error(
            'PDF access denied. The uploaded PDF file cannot be accessed. Please ensure the file was uploaded correctly and storage permissions are properly configured.'
          )
        }

        // Re-throw the error to prevent silent fallback and fail the Inngest function
        throw new Error(
          `PDF processing failed: ${pdfError instanceof Error ? pdfError.message : 'Unknown error'}`
        )
      }
    } else if (event?.data?.blogUrl) {
      // --- Blog to Video flow ---
      try {
        const { markdown, blog_images: imgs } =
          await extractBlogMarkdownAndImages(event.data.blogUrl)
        blog_images = imgs
        userPrompt = `Create a detailed video script based on the following blog article (in Markdown):\n\n${markdown}\n\n**Tone**: ${tone}\n**Target Audience**: ${audience}\n**Platform**: ${platform}\n**Duration**: ${duration} seconds\n**Language**: ${language}\n${keywords ? `**Keywords to include**: ${keywords}` : ''}\n${hook ? '**Include Hook**: Yes - Create an attention-grabbing opening' : ''}\n${callToAction ? '**Include Call-to-Action**: Yes - End with a clear next step' : ''}\n\n**Requirements**:\n1. Split the script into ${Math.ceil(duration / 10)} clearly defined scenes (approximately 8-12 seconds each)\n2. Each scene should have:\n   - Scene number\n   - Narration text (1-2 sentences, concise and engaging)\n   - Visual description keywords (what should be shown on screen)\n3. Make the script ${tone} in tone and appropriate for ${audience}\n4. Optimize for ${platform} format and audience expectations\n5. Keep the total duration around ${duration} seconds\n6. Make it visually engaging and easy to follow\n7. summary(summarize outline of script)`
      } catch {
        console.error('Blog extraction failed')
        // Fallback: send blogUrl directly to OpenAI
        userPrompt = `Create a detailed video script based on the following requirements:\n\n**Video Concept**: ${event.data.blogUrl}\n**Tone**: ${tone}\n**Target Audience**: ${audience}\n**Platform**: ${platform}\n**Duration**: ${duration} seconds\n**Language**: ${language}\n${keywords ? `**Keywords to include**: ${keywords}` : ''}\n${hook ? '**Include Hook**: Yes - Create an attention-grabbing opening' : ''}\n${callToAction ? '**Include Call-to-Action**: Yes - End with a clear next step' : ''}\n\n**Requirements**:\n1. Split the script into ${Math.ceil(duration / 10)} clearly defined scenes (approximately 8-12 seconds each)\n2. Each scene should have:\n   - Scene number\n   - Narration text (1-2 sentences, concise and engaging)\n   - Visual description keywords (what should be shown on screen)\n \n3. Make the script ${tone} in tone and appropriate for ${audience}\n4. Optimize for ${platform} format and audience expectations\n5. Keep the total duration around ${duration} seconds\n6. Make it visually engaging and easy to follow\n7. summary(summarize outline of script)`
        blog_images = []
      }
    } else if (event?.data?.podcastUrl || event?.data?.audioUrl) {
      // --- Podcast to Video flow or Audio to Video flow ---
      const podcastUrl = event.data.podcastUrl || event.data.audioUrl
      projectName =
        event.data.episodeTitle || event.data.audioName || 'Podcast Project'

      // Get transcription service from event data, default to lemonfox
      const transcriptionService: TranscriptionService = 'lemonfox'

      // Transcribe audio using the selected service
      const transcriptionResult = await transcribeAudio(
        podcastUrl,
        transcriptionService
      )

      // Process words to scenes using the transcription service
      const clipPace =
        (event.data.clipPace as 'fast' | 'medium' | 'slow' | 'verySlow') ||
        'medium'
      const captions = processWordsToScenes(
        transcriptionResult.words,
        clipPace,
        transcriptionService
      )

      // 4. Build speech object with clean audio URL
      const speechObj = {
        src: podcastUrl,
        name: projectName,
        volume: 100,
        enabled: true,
        transcript: {
          captions,
          status: 'COMPLETED' as const,
        },
      }
      speech = speechObj
      // 5. Build scenes directly from captions
      const scenes = await Promise.all(
        captions.map(async (caption, idx) => {
          // Fetch media for this scene
          const media = await fetchMediaForScene(
            caption.sentence,
            event?.data.autopick || 'mix',
            orientation || 'landscape',
            userId,
            organizationId
          )
          return {
            id: `scene_${idx + 1}`,
            text: caption.sentence,
            startOffset: caption.start,
            duration: caption.end - caption.start,
            captions: [],
            voiceSettings: null,
            media: media
              ? {
                  type: media.type === 'video' ? 'Video' : 'Image',
                  position: media.position,
                  size: media.size,
                  url: media.url,
                  thumbnail: media.thumbnail,
                  fit: 'blur',
                  transition: 'fade',
                  kenBurns: 'zoom-out',
                  effectDuration: 2,
                }
              : null,
            title: null,
          }
        })
      )

      // Build project JSON
      const project = {
        userId,
        projectName,
        method,
        createdAt: now,
        updatedAt: now,
        coverColor,
        coverPic,
        orientation: orientation || 'portrait',
        duration: validateDuration(
          event.data.episodeDuration || event.data.duration
        ).toString(),
        summary: event.data.episodeDescription,
        music,
        speech,
        captionSettings: captionSettings,
        backgroundVideo,
        scenes,
        voiceRegenerations: 0,
      }
      // Save to Projects DB (same as before)
      const allImages: string[] = []
      const insertData: InferInsertModel<typeof projects> = {
        userId,
        projectName,
        method,
        createdAt: new Date(now),
        updatedAt: new Date(now),
        coverColor,
        coverPic,
        orientation: orientation || 'portrait',
        duration: validateDuration(
          event.data.episodeDuration || event.data.duration
        ).toString(),
        summary: event.data.episodeDescription,
        music,
        speech,
        captionSettings: captionSettings,
        backgroundVideo,
        scenes,
        eventId: event?.data?.eventId || null,
        runId: event?.data?.runId || null,
        blogImages: allImages,
        organizationId: event?.data?.organizationId || null,
        voiceRegenerations: 0,
      }
      const [inserted] = await db
        .insert(projects)
        .values(insertData)
        .returning({ projectId: projects.projectId })
      return {
        ...project,
        projectId: inserted.projectId,
      }
    } else {
      // --- Idea/Text to Video flow (existing logic) ---
      userPrompt = `Create a detailed video script based on the following requirements:\n\n**Video Concept**: ${idea}\n**Tone**: ${tone}\n**Target Audience**: ${audience}\n**Platform**: ${platform}\n**Duration**: ${duration} seconds\n**Language**: ${language}\n${keywords ? `**Keywords to include**: ${keywords}` : ''}\n${hook ? '**Include Hook**: Yes - Create an attention-grabbing opening' : ''}\n${callToAction ? '**Include Call-to-Action**: Yes - End with a clear next step' : ''}\n\n**Requirements**:\n1. Split the script into ${Math.ceil(duration / 10)} clearly defined scenes (approximately 8-12 seconds each)\n2. Each scene should have:\n   - Scene number\n   - Narration text (1-2 sentences, concise and engaging)\n   - Visual description keywords (what should be shown on screen)\n \n3. Make the script ${tone} in tone and appropriate for ${audience}\n4. Optimize for ${platform} format and audience expectations\n5. Keep the total duration around ${duration} seconds\n6. Make it visually engaging and easy to follow\n7. summary(summarize outline of script)`
    }

    // Debug: Log the user prompt to verify PDF content is being used
    console.log('User prompt length:', userPrompt.length)
    console.log(
      'User prompt preview (first 500 chars):',
      userPrompt.substring(0, 500)
    )
    if (userPrompt.includes('PDF document content')) {
      console.log('✓ PDF content detected in prompt')
    } else {
      console.log('✗ No PDF content detected in prompt')
    }

    const completion = await openai.chat.completions.parse({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      response_format: zodResponseFormat(ScriptSchema, 'video_script'),
      max_tokens: 2000,
      temperature: 0.7,
    })
    script = completion.choices[0].message.parsed
    if (!script) throw new Error('OpenAI did not return a valid script')
    console.log('Step 1: OpenAI script generation complete')

    const summary = script.summary

    // --- Step 2: Process scenes (media, voiceover, captions) ---
    console.log('Step 2: Processing scenes (media, voiceover, captions)')

    type SceneVoiceoverResult = {
      audioUrl: string
      audioDuration: number
      alignment: {
        characters: string[]
        character_start_times_seconds: number[]
        character_end_times_seconds: number[]
      }
    }

    interface ElevenVoice {
      voice_id: string
      name?: string
    }

    async function processWithConcurrencyLimit<T, R>(
      items: T[],
      processor: (item: T, index: number) => Promise<R>,
      limit = 3
    ): Promise<R[]> {
      const results: R[] = []
      for (let i = 0; i < items.length; i += limit) {
        const batch = items.slice(i, i + limit)
        const batchPromises = batch.map((item, batchIndex) =>
          processor(item, i + batchIndex)
        )
        const batchResults = await Promise.allSettled(batchPromises)
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results[i + index] = result.value
          } else {
            results[i + index] = undefined as unknown as R // or handle error
          }
        })
      }
      return results
    }

    // Helper function using object-hash
    function createRequestHash(text: string, voiceId: string): string {
      return hash.MD5({ text, voiceId })
    }

    // Get audio duration using ffprobe as fallback
    async function getAudioDuration(audioBuffer: Buffer): Promise<number> {
      try {
        const tempPath = `/tmp/audio-${Date.now()}.mp3`
        fs.writeFileSync(tempPath, audioBuffer)
        try {
          const info = (await (
            ffprobe as (path: string, opts: unknown) => Promise<unknown>
          )(tempPath, { path: ffprobeStatic.path })) as {
            streams: { duration?: string }[]
          }
          const duration = info.streams[0]?.duration
          return duration ? parseFloat(duration) : 10
        } finally {
          fs.unlinkSync(tempPath)
        }
      } catch (error) {
        console.warn('Failed to get audio duration with ffprobe:', error)
        return 10 // fallback duration
      }
    }

    async function uploadAudioBufferToSupabase(
      audioBuffer: Buffer,
      filePath: string,
      bucket: string
    ): Promise<string> {
      try {
        const { error: uploadError } = await supabase.storage
          .from(bucket)
          .upload(filePath, audioBuffer, {
            contentType: 'audio/mpeg',
            upsert: false,
          })
        if (uploadError) {
          console.error('Supabase audio upload error:', uploadError)
          throw new Error(`Upload failed: ${uploadError.message}`)
        }

        // Track storage usage for the organization (non-blocking)
        try {
          const { incrementUsage } = await import('@/lib/usage-utils')
          const referenceId = organizationId || userId
          // Convert bytes to MB (1 MB = 1024 * 1024 bytes)
          const audioSizeInMB = Math.ceil(audioBuffer.length / (1024 * 1024))
          incrementUsage(referenceId, 'storage', audioSizeInMB).catch(error => {
            console.warn(
              'Failed to track storage usage for audio upload:',
              error
            )
          })
        } catch (error) {
          console.warn('Failed to import usage utils for audio upload:', error)
        }

        // Construct and return the public URL
        const SUPABASE_URL =
          process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
        return `${SUPABASE_URL}/storage/v1/object/public/${bucket}/${filePath}`
      } catch (error) {
        console.error('Error uploading audio buffer to Supabase:', error)
        throw error
      }
    }

    async function getForcedAlignment(
      audioUrl: string,
      text: string
    ): Promise<{
      characters: string[]
      character_start_times_seconds: number[]
      character_end_times_seconds: number[]
    } | null> {
      try {
        const ELEVEN_API_KEY = process.env.ELEVEN_API_KEY
        if (!ELEVEN_API_KEY) {
          console.error('ElevenLabs API key not configured')
          return null
        }
        const audioResponse = await fetch(audioUrl)
        if (!audioResponse.ok) {
          console.error('Failed to download audio file for alignment')
          return null
        }
        const audioBuffer = Buffer.from(await audioResponse.arrayBuffer())
        const form: FormData = new FormData()
        form.append('file', audioBuffer, {
          filename: 'audio.mp3',
          contentType: 'audio/mpeg',
        })
        form.append('text', text)
        const response = await fetch(
          'https://api.elevenlabs.io/v1/forced-alignment',
          {
            method: 'POST',
            headers: {
              'xi-api-key': ELEVEN_API_KEY,
              ...form.getHeaders(),
            },
            body: form,
          }
        )
        if (!response.ok) {
          console.error('Forced alignment API error:', response.status)
          return null
        }
        const data = (await response.json()) as {
          characters: Array<{ text: string; start: number; end: number }>
          words: Array<{
            text: string
            start: number
            end: number
            loss: number
          }>
          loss: number
        }
        const characters = data.characters.map(c => c.text)
        const character_start_times_seconds = data.characters.map(c => c.start)
        const character_end_times_seconds = data.characters.map(c => c.end)
        console.log('✅ Forced alignment completed with loss:', data.loss)
        return {
          characters,
          character_start_times_seconds,
          character_end_times_seconds,
        }
      } catch (error) {
        console.error('Error in forced alignment:', error)
        return null
      }
    }

    async function generateVoiceover(
      text: string,
      voice: ElevenVoice,
      userId: string
    ): Promise<SceneVoiceoverResult | null> {
      try {
        const ELEVEN_API_KEY = process.env.ELEVEN_API_KEY
        if (!ELEVEN_API_KEY) {
          console.error('ElevenLabs API key not configured')
          return null
        }
        const requestHash = createRequestHash(text, voice.voice_id)
        console.log('Request hash for voiceover:', requestHash)
        // Construct public URL for audio file directly in assets bucket, user folder, tts subfolder
        const SUPABASE_URL =
          process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
        const bucket = 'assets'
        const filePath = `${userId}/tts/${requestHash}.mp3`
        const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/${bucket}/${filePath}`
        // Check if the file exists by fetching the public URL
        const storageRes = await fetch(publicUrl)
        if (storageRes.ok) {
          console.log('✅ Found cached audio file (public)')
          // Use forced alignment for cached files
          console.log('🔄 Getting alignment data via forced alignment')
          const alignment = await getForcedAlignment(publicUrl, text)
          if (alignment) {
            const audioDuration =
              alignment.character_end_times_seconds.length > 0
                ? alignment.character_end_times_seconds[
                    alignment.character_end_times_seconds.length - 1
                  ]
                : 10
            return {
              audioUrl: publicUrl,
              audioDuration,
              alignment,
            }
          } else {
            // More accurate fallback: get duration from audio file
            console.log(
              '⚠️ Forced alignment failed, getting duration from audio file'
            )
            try {
              const audioResponse = await fetch(publicUrl)
              const audioBuffer = Buffer.from(await audioResponse.arrayBuffer())
              const audioDuration = await getAudioDuration(audioBuffer)
              return {
                audioUrl: publicUrl,
                audioDuration,
                alignment: {
                  characters: [],
                  character_start_times_seconds: [],
                  character_end_times_seconds: [],
                },
              }
            } catch (durationError) {
              console.error('Failed to get audio duration:', durationError)
              return {
                audioUrl: publicUrl,
                audioDuration: 10, // final fallback
                alignment: {
                  characters: [],
                  character_start_times_seconds: [],
                  character_end_times_seconds: [],
                },
              }
            }
          }
        }
        // If file does not exist, generate new audio
        console.log('🎵 Generating new audio with streaming timestamps')
        const url = `https://api.elevenlabs.io/v1/text-to-speech/${voice.voice_id}/stream/with-timestamps`
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'xi-api-key': ELEVEN_API_KEY,
          },
          body: JSON.stringify({
            text,
            model_id: 'eleven_multilingual_v2',
            voice_settings: {
              stability: 0.5,
              similarity_boost: 0.75,
            },
          }),
        })
        if (!response.ok) {
          console.error('ElevenLabs streaming API error:', response.status)
          throw new Error(
            `ElevenLabs API failed with status: ${response.status}`
          )
        }
        // Process streaming response using robust parser
        let audioBytes = Buffer.from('')
        let characters: string[] = []
        let characterStartTimesSeconds: number[] = []
        let characterEndTimesSeconds: number[] = []
        let buffer = ''
        // Use Node.js stream for reading
        const nodeStream = response.body as unknown as NodeJS.ReadableStream
        if (!nodeStream) {
          throw new Error('Failed to get response stream')
        }
        // Use async iterator for Node.js stream
        for await (const chunk of nodeStream) {
          buffer += chunk.toString('utf-8')
          const lines = buffer.split('\n')
          buffer = lines.pop()!
          for (const line of lines) {
            if (!line.trim()) continue
            try {
              const responseDict = JSON.parse(line)
              if (responseDict.audio_base64) {
                const audioBytesChunk = Buffer.from(
                  responseDict.audio_base64,
                  'base64'
                )
                audioBytes = Buffer.concat([audioBytes, audioBytesChunk])
              }
              if (responseDict.alignment) {
                characters = characters.concat(
                  responseDict.alignment.characters
                )
                characterStartTimesSeconds = characterStartTimesSeconds.concat(
                  responseDict.alignment.character_start_times_seconds
                )
                characterEndTimesSeconds = characterEndTimesSeconds.concat(
                  responseDict.alignment.character_end_times_seconds
                )
              }
            } catch (e) {
              console.error('JSON parsing error:', e, 'Line:', line)
            }
          }
        }
        if (audioBytes.length === 0) {
          throw new Error(
            'No audio data received from ElevenLabs streaming API'
          )
        }
        // Upload audio buffer to Supabase Storage in the correct path
        await uploadAudioBufferToSupabase(audioBytes, filePath, bucket)
        const alignment = {
          characters,
          character_start_times_seconds: characterStartTimesSeconds,
          character_end_times_seconds: characterEndTimesSeconds,
        }
        const audioDuration =
          characterEndTimesSeconds.length > 0
            ? characterEndTimesSeconds[characterEndTimesSeconds.length - 1]
            : await getAudioDuration(audioBytes)
        console.log(
          '✅ Audio with timestamps generated and uploaded to storage'
        )
        return {
          audioUrl: publicUrl,
          audioDuration,
          alignment,
        }
      } catch (error) {
        console.error('Error generating voiceover with timestamps:', error)
        throw new Error(
          `Voiceover generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      }
    }

    // --- Step 2: Process scenes (media, voiceover, captions) ---
    const processedScenes = await processWithConcurrencyLimit(
      script.scenes,
      async scene => {
        // For podcast flows, skip voiceover generation since we already have audio
        const isPodcastOrAudioFlow = speech !== null

        const media = await fetchMediaForScene(
          scene.searchKeywords,
          event?.data.autopick || 'mix',
          orientation || 'landscape',
          userId,
          organizationId
        )

        const voiceoverResult = isPodcastOrAudioFlow
          ? null // Skip voiceover for podcast flows
          : await generateVoiceover(scene.text, event?.data.voice, userId)

        return {
          ...scene,
          media: media || undefined,
          voiceoverResult: voiceoverResult || undefined,
        }
      },
      3
    )
    console.log('Step 2: Scene processing complete')

    // --- Step 3: Map processedScenes to project scene format ---
    console.log('Step 3: Mapping processed scenes to project format')
    let cumulativeOffset = 0
    const scenes = processedScenes.map(scene => {
      const { media, voiceoverResult, text, sceneNumber } = scene
      const isPodcastOrAudioFlow = speech !== null

      // Type guard for podcast scenes
      const podcastScene = isPodcastOrAudioFlow
        ? (scene as typeof scene & { duration: number; startOffset: number })
        : null

      // For podcast flows, use null voice settings since we have the original audio
      const voiceName = isPodcastOrAudioFlow
        ? null
        : event?.data.voice?.name || null
      const voiceId = isPodcastOrAudioFlow
        ? null
        : event?.data.voice?.voice_id || null
      const voiceUrl = isPodcastOrAudioFlow
        ? null
        : voiceoverResult?.audioUrl || null
      const voiceVol = isPodcastOrAudioFlow ? 0 : 100
      const voiceSpeed = isPodcastOrAudioFlow ? 1 : 1

      // For podcast flows, use the scene's duration from captions, otherwise use voiceover duration
      const duration = isPodcastOrAudioFlow
        ? podcastScene?.duration || 10
        : voiceoverResult?.audioDuration || 10

      // For podcast flows, use the scene's startOffset from captions
      const startOffset = isPodcastOrAudioFlow
        ? podcastScene?.startOffset || cumulativeOffset
        : cumulativeOffset

      if (!isPodcastOrAudioFlow) {
        cumulativeOffset += duration
      }

      let captions: unknown[] = []
      if (isPodcastOrAudioFlow && speech) {
        // For podcast flows, use the captions array from speech object
        const podcastSpeech = speech as {
          enabled: boolean
          src: string
          name: string
          volume: number
          transcript: {
            captions: {
              start: number
              end: number
              sentence: string
              wordBoundries: { start: number; end: number; word: string }[]
            }[]
            status: 'QUEUED' | 'COMPLETED' | 'FAILED'
          }
        }
        if (podcastSpeech.transcript?.captions) {
          captions = podcastSpeech.transcript.captions
        }
      } else if (voiceoverResult && voiceoverResult.alignment) {
        // Use legacy caption generation function for Inngest compatibility
        captions = generateLegacyCaptionsFromElevenLabs(
          text,
          voiceoverResult.alignment,
          duration
        )
      }
      const mappedMedia = media
        ? {
            type: media.type === 'video' ? 'Video' : 'Image',
            position: media.position,
            size: media.size,
            url: media.url,
            thumbnail: media.thumbnail,
            fit: 'blur',
            transition: 'fade',
            kenBurns: 'zoom-out',
            effectDuration: 2,
          }
        : null
      const voiceSettings = {
        voiceName,
        voiceId,
        voiceUrl,
        voiceVol,
        voiceSpeed,
      }
      return {
        id: `scene_${sceneNumber}`,
        text,
        title: null,
        voiceSettings,
        startOffset,
        duration,
        media: mappedMedia,
        captions,
      }
    })
    console.log('Step 3: Scene mapping complete')

    // --- Step 4: Build project JSON ---
    console.log('Step 4: Building project JSON')
    // Calculate total duration as sum of all scene durations, rounded to 3 decimals
    const totalDuration = Number(
      scenes
        .reduce(
          (sum, scene) =>
            sum + (typeof scene.duration === 'number' ? scene.duration : 0),
          0
        )
        .toFixed(3)
    )
    const project = {
      userId,
      projectName,
      method,
      createdAt: now,
      updatedAt: now,
      coverColor,
      coverPic,
      orientation: orientation || 'portrait',
      duration: validateDuration(totalDuration).toString(),
      summary,
      music,
      speech,
      captionSettings: captionSettings,
      backgroundVideo,
      scenes,
    }
    console.log('Step 4: Project JSON built')

    // --- Step 5: Save to Projects DB ---
    console.log('Step 5: Saving project to DB')
    // Combine all extracted images
    const allImages = [...blog_images, ...pdf_images]

    const insertData: InferInsertModel<typeof projects> = {
      userId,
      projectName,
      method,
      createdAt: new Date(now),
      updatedAt: new Date(now),
      coverColor,
      coverPic,
      orientation: orientation || 'portrait',
      duration: validateDuration(totalDuration).toString(),
      summary,
      music,
      speech,
      captionSettings: captionSettings,
      backgroundVideo,
      scenes,
      eventId: event?.data?.runId || null,
      runId: event?.data?.runId || null,
      blogImages: allImages,
      organizationId: event?.data?.organizationId || null,
    }

    try {
      console.log('Inserting project with userId:', userId)
      const [inserted] = await db
        .insert(projects)
        .values(insertData)
        .returning({ projectId: projects.projectId })
      console.log(
        'Step 5: Project saved to DB with projectId:',
        inserted.projectId
      )

      // Increment usage for project creation
      try {
        // Use organization ID if available, otherwise use user ID
        const referenceId = event?.data?.organizationId || userId
        await incrementUsage(referenceId, 'projects', 1)
        console.log('✅ Usage incremented for project creation')
      } catch (usageError) {
        console.error('❌ Failed to increment usage:', usageError)
        // Don't fail the entire operation if usage tracking fails
      }

      return {
        ...project,
        projectId: inserted.projectId,
        ...(allImages.length > 0 ? { blog_images: allImages } : {}),
      }
    } catch (error) {
      console.error('Database insert error:', error)
      console.error('Insert data:', JSON.stringify(insertData, null, 2))
      throw new Error(
        `Failed to save project to database: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }
)
