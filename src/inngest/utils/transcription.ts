import OpenAI from 'openai'
import fs from 'fs'
import path from 'path'

export type TranscriptionService = 'whisper' | 'lemonfox'

export interface TranscriptionWord {
  word: string
  start: number
  end: number
  score?: number
}

export interface TranscriptionResult {
  words: TranscriptionWord[]
  text: string
  duration: number
  language: string
}

export async function transcribeAudio(
  audioUrl: string,
  service: TranscriptionService = 'lemonfox'
): Promise<TranscriptionResult> {
  console.log(`🔍 Calling ${service} API for transcription...`)

  if (service === 'whisper') {
    return await transcribeWithWhisper(audioUrl)
  } else {
    return await transcribeWithLemonfox(audioUrl)
  }
}

// Helper function to download audio to temp file with redirect following
async function downloadAudioToTempFile(
  audioUrl: string
): Promise<{ tempAudioPath: string; finalUrl: string; fileSizeMB: number }> {
  console.log('🔄 PODCAST URL REDIRECT: Original URL:', audioUrl)

  const audioRes = await fetch(audioUrl, {
    method: 'GET',
    redirect: 'follow',
    headers: {
      'User-Agent': 'Mozilla/5.0 (compatible; AdoriAI/1.0)',
      Accept: 'audio/*, */*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Cache-Control': 'no-cache',
      Referer: 'https://www.google.com/',
    },
  })

  if (!audioRes.ok) throw new Error(`Failed to download audio.`)

  const finalUrl = audioRes.url // Final URL after redirects
  console.log('✅ PODCAST URL REDIRECT: Final URL:', finalUrl)

  // Save audio to temp file
  const tempAudioPath = path.join('/tmp', `audio-${Date.now()}.mp3`)
  const audioBuffer = Buffer.from(await audioRes.arrayBuffer())
  fs.writeFileSync(tempAudioPath, audioBuffer)

  // Get file size for logging
  const stats = await fs.promises.stat(tempAudioPath)
  const fileSizeMB = stats.size / (1024 * 1024)
  console.log(`📊 Audio file size: ${fileSizeMB.toFixed(2)} MB`)

  return { tempAudioPath, finalUrl, fileSizeMB }
}

async function transcribeWithWhisper(
  audioUrl: string
): Promise<TranscriptionResult> {
  // Download audio to temp file
  const { tempAudioPath } = await downloadAudioToTempFile(audioUrl)
  try {
    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY })
    const whisperRes = await openai.audio.transcriptions.create({
      file: fs.createReadStream(tempAudioPath),
      model: 'whisper-1',
      response_format: 'verbose_json',
      timestamp_granularities: ['word'],
    })

    const {
      words = [],
      text = '',
      duration = 0,
      language = 'unknown',
    } = whisperRes

    if (!words || !Array.isArray(words)) {
      throw new Error('Whisper API did not return word timings')
    }

    return {
      words: words.map(w => ({
        word: w.word,
        start: w.start,
        end: w.end,
      })),
      text,
      duration,
      language,
    }
  } finally {
    // Clean up temp file
    if (fs.existsSync(tempAudioPath)) {
      fs.unlinkSync(tempAudioPath)
    }
  }
}

async function transcribeWithLemonfox(
  audioUrl: string
): Promise<TranscriptionResult> {
  // Download audio to temp file
  // const { tempAudioPath, fileSizeMB } = await downloadAudioToTempFile(audioUrl)
  try {
    // if (fileSizeMB > 1024) {
    //   throw new Error(
    //     `Audio file too large: ${fileSizeMB.toFixed(2)} MB. Lemonfox supports max 1GB.`
    //   )
    // }

    // If (<= 100MB), use OpenAI library with Lemonfox base URL for direct file upload
    // if (fileSizeMB <= 100) {
    //   const lemonfox = new OpenAI({
    //     apiKey: process.env.LEMONFOX_API_KEY!,
    //     baseURL: 'https://api.lemonfox.ai/v1',
    //   })

    //   const lemonfoxRes = await lemonfox.audio.transcriptions.create({
    //     file: fs.createReadStream(tempAudioPath),
    //     model: 'whisper-1',
    //     response_format: 'verbose_json',
    //     timestamp_granularities: ['word'],
    //   })

    //   const { words, text, duration, language } = lemonfoxRes

    //   if (!words || !Array.isArray(words)) {
    //     throw new Error('Lemonfox API did not return word timings')
    //   }

    //   return {
    //     words: words.map(w => ({
    //       word: w.word,
    //       start: w.start,
    //       end: w.end,
    //       score: 'score' in w ? (w as { score: number }).score : undefined,
    //     })),
    //     text,
    //     duration,
    //     language,
    //   }
    // }

    // else If file > 100MB, use URL-based upload with Lemonfox fetch API
    const body = new FormData()
    body.append('file', audioUrl)
    body.append('response_format', 'verbose_json')
    body.append('timestamp_granularities[]', 'word')

    const resp = await fetch(
      'https://api.lemonfox.ai/v1/audio/transcriptions',
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${process.env.LEMONFOX_API_KEY}`,
        },
        body,
      }
    )

    if (!resp.ok) {
      throw new Error(`Lemonfox API error: ${resp.status} ${resp.statusText}`)
    }

    const lemonfoxData = (await resp.json()) as {
      task: string
      language: string
      duration: number
      text: string
      words: Array<{
        word: string
        start: number
        end: number
        score?: number
      }>
    }

    const { words, text, duration, language } = lemonfoxData

    if (!words || !Array.isArray(words)) {
      throw new Error('Lemonfox API did not return word timings')
    }

    return {
      words: words.map(w => ({
        word: w.word,
        start: w.start,
        end: w.end,
        score: w.score,
      })),
      text,
      duration,
      language,
    }
  } catch (error) {
    console.error('Lemonfox transcription error:', error)
    throw error
  }
  // finally {
  //   // Clean up temp file
  //   if (fs.existsSync(tempAudioPath)) {
  //     fs.unlinkSync(tempAudioPath)
  //   }
  // }
}

export function processWordsToScenes(
  words: TranscriptionWord[],
  clipPace: 'fast' | 'medium' | 'slow' | 'verySlow' = 'medium',
  service: TranscriptionService = 'lemonfox'
) {
  // Map clipPace to min duration
  const paceMap = { fast: 5, medium: 8, slow: 12, verySlow: 15 }
  const MIN_SENTENCE_DURATION = paceMap[clipPace] || 6

  console.log(
    `🎬 [${service}] Processing with clipPace: ${clipPace}, MIN_SENTENCE_DURATION: ${MIN_SENTENCE_DURATION}s`
  )

  const captions = []
  let currentWords: TranscriptionWord[] = []
  let sentenceStart: number | null = null

  for (const wordObj of words) {
    if (currentWords.length === 0) {
      sentenceStart = wordObj.start
    }
    currentWords.push(wordObj)

    const sentenceEnd = wordObj.end
    const duration = sentenceEnd - (sentenceStart || 0)

    // Check for punctuation to break sentences more naturally
    const isPunctuation = /[.!?]/.test(wordObj.word)

    // For Lemonfox, be more strict about duration to avoid short scenes
    // For Whisper, allow more natural breaks
    const shouldBreak =
      service === 'lemonfox'
        ? (duration >= MIN_SENTENCE_DURATION && isPunctuation) ||
          duration >= MIN_SENTENCE_DURATION * 1.2
        : duration >= MIN_SENTENCE_DURATION || isPunctuation

    if (shouldBreak) {
      const sentence = currentWords
        .map(w => w.word)
        .join(' ')
        .trim()
      if (sentenceStart !== null) {
        captions.push({
          start: sentenceStart,
          end: sentenceEnd,
          sentence,
          wordBoundries: currentWords.map(w => ({
            start: w.start,
            end: w.end,
            word: w.word,
          })),
        })
      }
      currentWords = []
      sentenceStart = null
    }
  }

  // Add any remaining words as the last caption
  if (currentWords.length > 0 && sentenceStart !== null) {
    const sentence = currentWords
      .map(w => w.word)
      .join(' ')
      .trim()
    captions.push({
      start: sentenceStart,
      end: currentWords[currentWords.length - 1].end,
      sentence,
      wordBoundries: currentWords.map(w => ({
        start: w.start,
        end: w.end,
        word: w.word,
      })),
    })
  }

  return captions
}
