import { createClient } from '@supabase/supabase-js'

// Top-level supabase client for all helpers
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Helper function to upload AI-generated image to Supabase storage
async function uploadAIImageToSupabase(
  base64Image: string,
  userId: string,
  prompt: string
): Promise<string> {
  try {
    // Remove data URL prefix to get just the base64 data
    const base64Data = base64Image.replace(/^data:image\/jpeg;base64,/, '')

    // Convert base64 to buffer
    const imageBuffer = Buffer.from(base64Data, 'base64')

    // Create a unique filename based on prompt and timestamp
    const timestamp = Date.now()
    const promptHash = prompt.replace(/[^a-zA-Z0-9]/g, '').substring(0, 20)
    const fileName = `ai-${promptHash}-${timestamp}.jpg`
    const filePath = `${userId}/images/${fileName}`

    // Upload to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('assets')
      .upload(filePath, imageBuffer, {
        contentType: 'image/jpeg',
        upsert: false,
      })

    if (uploadError) {
      console.error('Supabase AI image upload error:', uploadError)
      throw new Error(`Upload failed: ${uploadError.message}`)
    }

    // Construct and return the public URL
    const SUPABASE_URL =
      process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL
    const publicUrl = `${SUPABASE_URL}/storage/v1/object/public/assets/${filePath}`

    console.log('✅ AI image uploaded to Supabase:', publicUrl)
    return publicUrl
  } catch (error) {
    console.error('Error uploading AI image to Supabase:', error)
    throw error
  }
}

// --- Types for external API responses ---
interface UnsplashImage {
  id: string
  urls: { regular: string; small: string }
  width: number
  height: number
}
interface PexelsImage {
  id: string
  src: { large: string; medium: string; small: string }
  width: number
  height: number
}
interface PixabayImage {
  id: number
  largeImageURL: string
  webformatURL: string
  imageWidth: number
  imageHeight: number
}
interface PexelsVideo {
  id: number
  video_files: { link: string; width: number; height: number }[]
  image: string
}
interface AIImageResult {
  imageUrl: string
  width: number
  height: number
}

type SceneMedia = {
  id: string
  type: 'image' | 'video'
  url: string
  thumbnail: string
  position: { x: number; y: number }
  size: { width: number; height: number }
  startTime: number
  endTime: number
  duration: number
}

export async function fetchUnsplashImages(
  query: string,
  orientation: string = 'landscape'
): Promise<UnsplashImage[]> {
  try {
    const UNSPLASH_CLIENT_ID = process.env.UNSPLASH_CLIENT_ID
    if (!UNSPLASH_CLIENT_ID) {
      console.error('Unsplash client ID not configured')
      return []
    }
    const url = `https://api.unsplash.com/search/photos?query=${encodeURIComponent(query)}&per_page=10&orientation=${orientation}`
    const response = await fetch(url, {
      headers: {
        Authorization: `Client-ID ${UNSPLASH_CLIENT_ID}`,
      },
    })
    if (!response.ok) {
      console.error('Failed to fetch Unsplash images:', response.status)
      return []
    }
    const data = (await response.json()) as { results?: UnsplashImage[] }
    return data.results || []
  } catch (error) {
    console.error('Error fetching Unsplash images:', error)
    return []
  }
}

export async function fetchPexelsImages(
  query: string,
  orientation: string = 'landscape'
): Promise<PexelsImage[]> {
  try {
    const PEXELS_API_KEY = process.env.PEXELS_API_KEY
    if (!PEXELS_API_KEY) {
      console.error('Pexels API key not configured')
      return []
    }
    let url = `https://api.pexels.com/v1/search?query=${encodeURIComponent(query)}&per_page=10`
    if (orientation && orientation !== 'all') {
      url += `&orientation=${orientation}`
    }
    const response = await fetch(url, {
      headers: {
        Authorization: PEXELS_API_KEY,
      },
    })
    if (!response.ok) {
      console.error('Failed to fetch Pexels images:', response.status)
      return []
    }
    const data = (await response.json()) as { photos?: PexelsImage[] }
    return data.photos || []
  } catch (error) {
    console.error('Error fetching Pexels images:', error)
    return []
  }
}

export async function fetchPixabayImages(
  query: string,
  orientation: string = 'landscape'
): Promise<PixabayImage[]> {
  try {
    const PIXABAY_API_KEY = process.env.PIXABAY_API_KEY
    if (!PIXABAY_API_KEY) {
      console.error('Pixabay API key not configured')
      return []
    }
    // Map orientation values to Pixabay's orientation parameter
    let pixabayOrientation = 'all'
    if (orientation === 'landscape') pixabayOrientation = 'horizontal'
    if (orientation === 'portrait') pixabayOrientation = 'vertical'
    if (orientation === 'square') pixabayOrientation = 'all'
    const url = `https://pixabay.com/api/?key=${PIXABAY_API_KEY}&q=${encodeURIComponent(query)}&image_type=photo&per_page=10&safesearch=true&orientation=${pixabayOrientation}`
    const response = await fetch(url)
    if (!response.ok) {
      console.error('Failed to fetch Pixabay images:', response.status)
      return []
    }
    const data = (await response.json()) as { hits?: PixabayImage[] }
    return data.hits || []
  } catch (error) {
    console.error('Error fetching Pixabay images:', error)
    return []
  }
}

export async function fetchPexelsVideos(
  query: string,
  orientation: string = 'landscape'
): Promise<PexelsVideo[]> {
  try {
    const PEXELS_API_KEY = process.env.PEXELS_API_KEY
    if (!PEXELS_API_KEY) {
      console.error('Pexels API key not configured')
      return []
    }
    let url = `https://api.pexels.com/videos/search?query=${encodeURIComponent(query)}&per_page=10`
    if (orientation && orientation !== 'all') {
      url += `&orientation=${orientation}`
    }
    const response = await fetch(url, {
      headers: {
        Authorization: PEXELS_API_KEY,
      },
    })
    if (!response.ok) {
      console.error('Failed to fetch Pexels videos:', response.status)
      return []
    }
    const data = (await response.json()) as { videos?: PexelsVideo[] }
    return data.videos || []
  } catch (error) {
    console.error('Error fetching Pexels videos:', error)
    return []
  }
}

export async function generateAIImage(
  prompt: string,
  orientation: string = 'landscape',
  userId?: string,
  organizationId?: string
): Promise<AIImageResult | null> {
  try {
    const GETIMG_API_KEY = process.env.GETIMG_API_KEY
    if (!GETIMG_API_KEY) {
      console.error('GetImg.ai API key not configured')
      return null
    }
    // Try FLUX first (preferred model)
    const fluxResult = await tryGenerateWithGetImgAPI(
      prompt,
      orientation,
      'flux-1-schnell',
      userId,
      organizationId
    )
    if (fluxResult) {
      console.log('✅ Successfully generated with FLUX model')
      return fluxResult
    }
    // Fallback to Stable Diffusion XL if FLUX fails
    console.log('🔄 FLUX failed, trying Stable Diffusion XL...')
    const sdxlResult = await tryGenerateWithGetImgAPI(
      prompt,
      orientation,
      'stable-diffusion-xl',
      userId,
      organizationId
    )
    if (sdxlResult) {
      console.log('✅ Successfully generated with Stable Diffusion XL')
      return sdxlResult
    }
    console.error('❌ Both FLUX and Stable Diffusion XL failed')
    return null
  } catch (error) {
    console.error('Error in generateAIImage:', error)
    return null
  }
}

// Helper function to try generating with a specific model via direct GetImg.ai API
export async function tryGenerateWithGetImgAPI(
  prompt: string,
  orientation: string,
  model: string,
  userId?: string,
  organizationId?: string
): Promise<AIImageResult | null> {
  try {
    const GETIMG_API_KEY = process.env.GETIMG_API_KEY
    if (!GETIMG_API_KEY) {
      console.error('GetImg.ai API key not configured')
      return null
    }
    // Map model to GetImg.ai endpoint
    const modelEndpoints: Record<string, string> = {
      'stable-diffusion-xl':
        'https://api.getimg.ai/v1/stable-diffusion-xl/text-to-image',
      'flux-1-schnell': 'https://api.getimg.ai/v1/flux-schnell/text-to-image',
    }
    const endpoint = modelEndpoints[model]
    if (!endpoint) {
      console.error(`Unsupported model: ${model}`)
      return null
    }
    // Handle different model parameter requirements
    let dimensionMap: Record<string, { width: number; height: number }>
    if (model === 'flux-1-schnell') {
      // FLUX models use width/height with standard 16:9 ratios
      dimensionMap = {
        landscape: { width: 1024, height: 576 }, // 16:9 landscape
        portrait: { width: 576, height: 1024 }, // 9:16 portrait
        square: { width: 1024, height: 1024 }, // 1:1 square
      }
    } else {
      // Stable Diffusion models use standard dimensions
      dimensionMap = {
        landscape: { width: 1024, height: 576 },
        portrait: { width: 576, height: 1024 },
        square: { width: 1024, height: 1024 },
      }
    }
    const dimensions = dimensionMap[orientation] || dimensionMap.landscape
    // Build request body based on model
    let requestBody: Record<string, unknown>
    if (model === 'flux-1-schnell') {
      // FLUX.1 schnell - only supports basic parameters
      requestBody = {
        prompt: prompt,
        output_format: 'jpeg',
        width: dimensions.width,
        height: dimensions.height,
      }
    } else {
      // Stable Diffusion models - support steps parameter
      requestBody = {
        prompt: prompt,
        width: dimensions.width,
        height: dimensions.height,
        output_format: 'jpeg',
        steps: 20, // Standard for Stable Diffusion models
      }
    }
    console.log(
      `🎨 ${model} - ${orientation} (${dimensions.width}x${dimensions.height})`
    )
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${GETIMG_API_KEY}`,
      },
      body: JSON.stringify(requestBody),
    })
    if (!response.ok) {
      const errorText = await response.text()
      console.error(
        `${model} generation failed: ${response.status} - ${errorText}`
      )
      if (response.status === 402) {
        console.error('GetImg.ai API: Payment required - check credits/billing')
      }
      return null
    }
    const data = (await response.json()) as { image: string }
    const base64ImageUrl = `data:image/jpeg;base64,${data.image}`

    // Upload to Supabase if userId is provided
    let finalImageUrl = base64ImageUrl
    if (userId) {
      try {
        finalImageUrl = await uploadAIImageToSupabase(
          base64ImageUrl,
          userId,
          prompt
        )
      } catch (uploadError) {
        console.warn(
          'Failed to upload AI image to Supabase, using base64:',
          uploadError
        )
        // Continue with base64 if upload fails
      }
    }

    // Track AI image usage (non-blocking)
    try {
      const { incrementUsage } = await import('@/lib/usage-utils')
      const referenceId = organizationId || userId
      if (referenceId) {
        incrementUsage(referenceId, 'aiImages', 1).catch(error => {
          console.warn('Failed to track AI image usage:', error)
        })
        console.log('✅ AI image usage tracked for referenceId:', referenceId)
      }
    } catch (error) {
      console.warn('Failed to import usage utils for AI image tracking:', error)
    }

    return {
      imageUrl: finalImageUrl,
      width: dimensions.width,
      height: dimensions.height,
    }
  } catch (error) {
    console.error(`Error generating with ${model}:`, error)
    return null
  }
}

// --- Media Fetcher ---
export async function fetchMediaForScene(
  searchPrompt: string,
  autopick: string,
  orientation: string,
  userId?: string,
  organizationId?: string
): Promise<SceneMedia | null> {
  try {
    const orientationParam =
      orientation === 'landscape'
        ? 'landscape'
        : orientation === 'portrait'
          ? 'portrait'
          : 'landscape'

    let mediaUrl = ''
    let thumbnailUrl = ''
    let width = 1920
    let height = 1080
    let mediaType: 'image' | 'video' = 'image'

    switch (autopick) {
      case 'stock-videos': {
        const videos = await fetchPexelsVideos(searchPrompt, orientationParam)
        if (videos.length > 0) {
          const video = videos[0]
          mediaUrl = video.video_files?.[0]?.link || ''
          thumbnailUrl = video.image || ''
          width = video.video_files?.[0]?.width || 1920
          height = video.video_files?.[0]?.height || 1080
          mediaType = 'video'
        }
        break
      }
      case 'stock-images': {
        const unsplashImages = await fetchUnsplashImages(
          searchPrompt,
          orientationParam
        )
        if (unsplashImages.length > 0) {
          const photo = unsplashImages[0]
          mediaUrl = photo.urls?.regular || ''
          thumbnailUrl = photo.urls?.small || ''
          width = photo.width || 1920
          height = photo.height || 1080
        }
        break
      }
      case 'ai-images': {
        // Generate AI image using GetImg.ai
        const enhancedPrompt = `${searchPrompt}, high quality, professional, detailed, vibrant colors, ${orientation === 'portrait' ? 'portrait orientation' : orientation === 'square' ? 'square composition' : 'landscape orientation'}`
        const aiImageResult = await generateAIImage(
          enhancedPrompt,
          orientation,
          userId,
          organizationId
        )
        if (aiImageResult) {
          mediaUrl = aiImageResult.imageUrl
          thumbnailUrl = aiImageResult.imageUrl
          width = aiImageResult.width
          height = aiImageResult.height
        } else {
          // Fallback to stock images if AI generation fails
          const fallbackImages = await fetchUnsplashImages(
            searchPrompt,
            orientationParam
          )
          if (fallbackImages.length > 0) {
            const photo = fallbackImages[0]
            mediaUrl = photo.urls?.regular || ''
            thumbnailUrl = photo.urls?.small || ''
            width = photo.width || 1920
            height = photo.height || 1080
          }
        }
        break
      }
      case 'mix':
      default: {
        const sources = ['pexels', 'unsplash', 'pixabay', 'ai-generated']
        const randomSource = sources[Math.floor(Math.random() * sources.length)]
        if (randomSource === 'pexels') {
          const pexelsImages = await fetchPexelsImages(
            searchPrompt,
            orientationParam
          )
          if (pexelsImages.length > 0) {
            const photo = pexelsImages[0]
            mediaUrl = photo.src?.large || photo.src?.medium || ''
            thumbnailUrl = photo.src?.medium || photo.src?.small || ''
            width = photo.width || 1920
            height = photo.height || 1080
          }
        } else if (randomSource === 'unsplash') {
          const unsplashImages = await fetchUnsplashImages(
            searchPrompt,
            orientationParam
          )
          if (unsplashImages.length > 0) {
            const photo = unsplashImages[0]
            mediaUrl = photo.urls?.regular || ''
            thumbnailUrl = photo.urls?.small || ''
            width = photo.width || 1920
            height = photo.height || 1080
          }
        } else if (randomSource === 'ai-generated') {
          const mixEnhancedPrompt = `${searchPrompt}, high quality, professional, detailed, vibrant colors, ${orientation === 'portrait' ? 'portrait orientation' : orientation === 'square' ? 'square composition' : 'landscape orientation'}`
          const mixAiResult = await generateAIImage(
            mixEnhancedPrompt,
            orientation,
            userId,
            organizationId
          )
          if (mixAiResult) {
            mediaUrl = mixAiResult.imageUrl
            thumbnailUrl = mixAiResult.imageUrl
            width = mixAiResult.width
            height = mixAiResult.height
          } else {
            const mixFallbackImages = await fetchUnsplashImages(
              searchPrompt,
              orientationParam
            )
            if (mixFallbackImages.length > 0) {
              const photo = mixFallbackImages[0]
              mediaUrl = photo.urls?.regular || ''
              thumbnailUrl = photo.urls?.small || ''
              width = photo.width || 1920
              height = photo.height || 1080
            }
          }
        } else {
          const pixabayImages = await fetchPixabayImages(
            searchPrompt,
            orientationParam
          )
          if (pixabayImages.length > 0) {
            const hit = pixabayImages[0]
            mediaUrl = hit.largeImageURL || hit.webformatURL || ''
            thumbnailUrl = hit.webformatURL || ''
            width = hit.imageWidth || 1920
            height = hit.imageHeight || 1080
          }
        }
        break
      }
    }

    if (!mediaUrl) return null

    let mediaDuration = 10
    if (mediaType === 'video') {
      mediaDuration = 15
    }

    return {
      id: `media-${Date.now()}-${Math.random()}`,
      type: mediaType,
      url: mediaUrl,
      thumbnail: thumbnailUrl,
      position: { x: 0, y: 0 },
      size: { width, height },
      startTime: 0,
      endTime: mediaDuration,
      duration: mediaDuration,
    }
  } catch (error) {
    console.error('Error fetching media:', error)
    return null
  }
}
