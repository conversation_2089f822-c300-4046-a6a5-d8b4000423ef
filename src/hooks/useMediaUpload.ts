'use client'

import { useState } from 'react'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import { toast } from '@/lib/toast'
import {
  generateVideoThumbnail,
  generateImageThumbnail,
  extractVideoMetadata,
  extractImageMetadata,
  determineVideoQuality,
} from '@/lib/client-thumbnail-generator'

export interface MediaUploadResult {
  id: string
  originalUrl: string
  thumbnailUrl?: string
  lowResUrl?: string
  metadata: {
    width: number
    height: number
    fileSize: number
    mimeType: string
    duration?: number
    quality?: string
    aspectRatio: number
    orientation: 'landscape' | 'portrait' | 'square'
    dominantColors?: string[]
  }
}

export interface UploadProgress {
  stage:
    | 'analyzing'
    | 'generating-thumbnail'
    | 'uploading-original'
    | 'uploading-thumbnail'
    | 'saving-metadata'
    | 'complete'
  progress: number
  message: string
}

export interface BatchUploadProgress {
  totalFiles: number
  completedFiles: number
  currentFile: string
  overallProgress: number
  fileProgresses: Record<string, UploadProgress>
}

export function useMediaUpload() {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(
    null
  )
  const [batchProgress, setBatchProgress] =
    useState<BatchUploadProgress | null>(null)

  const uploadMedia = async (
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<MediaUploadResult> => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated')
    }

    setIsUploading(true)
    const updateProgress = (progress: UploadProgress) => {
      setUploadProgress(progress)
      onProgress?.(progress)
    }

    updateProgress({
      stage: 'analyzing',
      progress: 10,
      message: 'Processing media file...',
    })

    try {
      const isImage = file.type.startsWith('image/')
      const isVideo = file.type.startsWith('video/')

      if (!isImage && !isVideo) {
        throw new Error('File must be an image or video')
      }

      // Extract metadata client-side
      let metadata: {
        width: number
        height: number
        fileSize: number
        mimeType: string
        duration?: number
        quality?: string
        aspectRatio: number
        orientation: 'landscape' | 'portrait' | 'square'
        dominantColors?: string[]
      }
      if (isImage) {
        const imageMetadata = await extractImageMetadata(file)
        metadata = {
          ...imageMetadata,
          fileSize: file.size,
          mimeType: file.type,
        }
      } else {
        const videoMetadata = await extractVideoMetadata(file)
        metadata = {
          ...videoMetadata,
          fileSize: file.size,
          mimeType: file.type,
          quality: determineVideoQuality(
            videoMetadata.width,
            videoMetadata.height
          ),
        }
      }

      updateProgress({
        stage: 'generating-thumbnail',
        progress: 30,
        message: 'Generating thumbnail...',
      })

      // Generate thumbnail client-side
      let thumbnailBlob: Blob | null = null
      try {
        if (isImage) {
          const thumbnailResult = await generateImageThumbnail(file, {
            width: 400,
            height: 300,
            quality: 0.8,
          })
          thumbnailBlob = thumbnailResult.blob
        } else {
          // For videos, use dimensions that maintain aspect ratio
          const maxWidth = 400
          const maxHeight = 600 // Allow taller thumbnails for portrait videos

          let thumbWidth = maxWidth
          let thumbHeight = maxHeight

          if (metadata.aspectRatio > 1) {
            // Landscape video
            thumbHeight = Math.round(maxWidth / metadata.aspectRatio)
          } else {
            // Portrait video
            thumbWidth = Math.round(maxHeight * metadata.aspectRatio)
          }

          const thumbnailResult = await generateVideoThumbnail(file, {
            width: thumbWidth,
            height: thumbHeight,
            quality: 0.8,
            timeOffset: 1,
          })
          thumbnailBlob = thumbnailResult.blob
        }
      } catch (error) {
        console.warn('Failed to generate thumbnail:', error)
        // Continue without thumbnail
      }

      updateProgress({
        stage: 'uploading-original',
        progress: 50,
        message: 'Initiating upload...',
      })

      // Create form data for the initiate API
      const formData = new FormData()
      formData.append('file', file)
      formData.append('metadata', JSON.stringify(metadata))

      // Add thumbnail if generated
      if (thumbnailBlob) {
        formData.append('thumbnail', thumbnailBlob, 'thumbnail.jpg')
      }

      // Initiate upload (fast response)
      const initiateResponse = await fetch('/api/media/initiate', {
        method: 'POST',
        body: formData,
      })

      if (!initiateResponse.ok) {
        const errorData = await initiateResponse.json()
        throw new Error(errorData.error || 'Upload initiation failed')
      }

      const { id: uploadId } = await initiateResponse.json()

      updateProgress({
        stage: 'uploading-original',
        progress: 60,
        message: 'Processing upload...',
      })

      // Poll for status updates
      const result = await pollUploadStatus(uploadId, updateProgress)

      updateProgress({
        stage: 'complete',
        progress: 100,
        message: 'Upload complete!',
      })

      // Transform the response to match our expected format
      const uploadResult: MediaUploadResult = {
        id: result.id,
        originalUrl: result.url,
        thumbnailUrl: result.thumbnail,
        lowResUrl: result.low_res_url,
        metadata: {
          width: result.width,
          height: result.height,
          fileSize: result.file_size,
          mimeType: result.metadata.mimeType,
          duration: result.duration,
          quality: result.quality,
          aspectRatio: result.metadata.aspectRatio,
          orientation: result.metadata.orientation,
          dominantColors: result.metadata.dominantColors,
        },
      }

      toast.success(`${isImage ? 'Image' : 'Video'} uploaded successfully!`)
      return uploadResult
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Upload failed'
      toast.error(errorMessage)
      throw error
    } finally {
      setIsUploading(false)
      setUploadProgress(null)
    }
  }

  const pollUploadStatus = async (
    uploadId: string,
    updateProgress: (progress: UploadProgress) => void
  ): Promise<{
    id: string
    url: string
    thumbnail: string
    low_res_url: string
    width: number
    height: number
    file_size: number
    duration?: number
    quality?: string
    metadata: {
      mimeType: string
      aspectRatio: number
      orientation: 'landscape' | 'portrait' | 'square'
      dominantColors?: string[]
    }
  }> => {
    const maxAttempts = 60 // 5 minutes max (5 second intervals)
    let attempts = 0

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(`/api/media/status/${uploadId}`)

        if (!response.ok) {
          throw new Error('Failed to check upload status')
        }

        const statusData = await response.json()

        // Update progress if available
        if (statusData.progress !== undefined) {
          updateProgress({
            stage:
              statusData.status === 'uploading'
                ? 'uploading-original'
                : 'saving-metadata',
            progress: statusData.progress,
            message: statusData.message || 'Processing...',
          })
        }

        if (statusData.status === 'complete') {
          return statusData.data
        }

        if (statusData.status === 'error') {
          throw new Error(statusData.error || 'Upload failed')
        }

        // Wait 3 seconds before next poll (faster updates)
        await new Promise(resolve => setTimeout(resolve, 3000))
        attempts++
      } catch (error) {
        console.error('Status polling error:', error)
        attempts++

        if (attempts >= maxAttempts) {
          throw new Error('Upload timeout - please try again')
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 3000))
      }
    }

    throw new Error('Upload timeout - please try again')
  }

  const uploadMultipleMedia = async (
    files: File[]
  ): Promise<MediaUploadResult[]> => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated')
    }

    const results: MediaUploadResult[] = []
    const fileProgresses: Record<string, UploadProgress> = {}

    // Initialize batch progress
    setBatchProgress({
      totalFiles: files.length,
      completedFiles: 0,
      currentFile: files[0]?.name || '',
      overallProgress: 0,
      fileProgresses,
    })

    try {
      // Upload all files concurrently
      const uploadPromises = files.map(async (file, index) => {
        const fileId = `file-${index}-${Date.now()}`

        const onFileProgress = (progress: UploadProgress) => {
          fileProgresses[fileId] = progress

          // Calculate overall progress
          const totalProgress = Object.values(fileProgresses).reduce(
            (sum, p) => sum + p.progress,
            0
          )
          const overallProgress = Math.round(totalProgress / files.length)

          setBatchProgress(prev =>
            prev
              ? {
                  ...prev,
                  currentFile: file.name,
                  overallProgress,
                  fileProgresses: { ...fileProgresses },
                }
              : null
          )
        }

        try {
          const result = await uploadMedia(file, onFileProgress)

          // Mark file as complete
          setBatchProgress(prev =>
            prev
              ? {
                  ...prev,
                  completedFiles: prev.completedFiles + 1,
                }
              : null
          )

          return result
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error)
          throw error
        }
      })

      // Wait for all uploads to complete
      const uploadResults = await Promise.all(uploadPromises)
      results.push(...uploadResults)

      return results
    } finally {
      setBatchProgress(null)
    }
  }

  return {
    uploadMedia,
    uploadMultipleMedia,
    isUploading,
    uploadProgress,
    batchProgress,
  }
}
