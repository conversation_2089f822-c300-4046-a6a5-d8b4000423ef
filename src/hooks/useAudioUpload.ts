import { useState } from 'react'
// import { useUser } from '@clerk/nextjs'
import { authClient } from '@/lib/auth-client'
import { toast } from '@/lib/toast'

export interface AudioUploadResult {
  id: string
  originalUrl: string
  metadata: {
    fileSize: number
    mimeType: string
    duration?: number
  }
}

export interface AudioUploadProgress {
  stage: 'analyzing' | 'uploading' | 'processing' | 'complete'
  progress: number
  message: string
}

export interface AudioBatchUploadProgress {
  totalFiles: number
  completedFiles: number
  currentFile: string
  overallProgress: number
  fileProgresses: Record<string, AudioUploadProgress>
}

export function useAudioUpload() {
  // const { user } = useUser()
  const { data: session } = authClient.useSession()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] =
    useState<AudioUploadProgress | null>(null)
  const [batchProgress, setBatchProgress] =
    useState<AudioBatchUploadProgress | null>(null)

  const uploadAudio = async (
    file: File,
    onProgress?: (progress: AudioUploadProgress) => void
  ): Promise<AudioUploadResult> => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated')
    }

    setIsUploading(true)
    const updateProgress = (progress: AudioUploadProgress) => {
      setUploadProgress(progress)
      onProgress?.(progress)
    }

    updateProgress({
      stage: 'analyzing',
      progress: 10,
      message: 'Processing audio file...',
    })

    try {
      const isAudio = file.type.startsWith('audio/')

      if (!isAudio) {
        throw new Error('File must be an audio file')
      }

      // Extract basic metadata
      const metadata = {
        fileSize: file.size,
        mimeType: file.type,
        duration: undefined as number | undefined,
      }

      // Try to get duration from audio file
      try {
        const audio = new Audio()
        const durationPromise = new Promise<number>((resolve, reject) => {
          audio.addEventListener('loadedmetadata', () => {
            resolve(audio.duration)
          })
          audio.addEventListener('error', () => {
            reject(new Error('Could not load audio metadata'))
          })
          audio.src = URL.createObjectURL(file)
        })

        metadata.duration = await durationPromise
      } catch (error) {
        console.warn('Could not extract audio duration:', error)
        // Continue without duration
      }

      updateProgress({
        stage: 'uploading',
        progress: 30,
        message: 'Initiating upload...',
      })

      // Create form data for the initiate API
      const formData = new FormData()
      formData.append('file', file)
      formData.append('metadata', JSON.stringify(metadata))

      // Initiate upload
      const initiateResponse = await fetch('/api/media/initiate', {
        method: 'POST',
        body: formData,
      })

      if (!initiateResponse.ok) {
        const errorData = await initiateResponse.json()
        throw new Error(errorData.error || 'Upload initiation failed')
      }

      const { id: uploadId } = await initiateResponse.json()

      updateProgress({
        stage: 'processing',
        progress: 60,
        message: 'Processing upload...',
      })

      // Poll for status updates
      const result = await pollUploadStatus(uploadId, updateProgress)

      updateProgress({
        stage: 'complete',
        progress: 100,
        message: 'Upload complete!',
      })

      const uploadResult: AudioUploadResult = {
        id: result.id,
        originalUrl: result.url,
        metadata: {
          fileSize: result.file_size,
          mimeType: result.metadata.mimeType,
          duration: result.duration,
        },
      }

      toast.success('Audio uploaded successfully!')
      return uploadResult
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Upload failed'
      toast.error(errorMessage)
      throw error
    } finally {
      setIsUploading(false)
      setUploadProgress(null)
    }
  }

  const pollUploadStatus = async (
    uploadId: string,
    updateProgress: (progress: AudioUploadProgress) => void
  ): Promise<{
    id: string
    url: string
    file_size: number
    duration?: number
    metadata: {
      mimeType: string
    }
  }> => {
    const maxAttempts = 60 // 5 minutes max (5 second intervals)
    let attempts = 0

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(`/api/media/status/${uploadId}`)

        if (!response.ok) {
          throw new Error('Failed to check upload status')
        }

        const statusData = await response.json()

        // Update progress if available
        if (statusData.progress !== undefined) {
          updateProgress({
            stage:
              statusData.status === 'uploading' ? 'uploading' : 'processing',
            progress: statusData.progress,
            message: statusData.message || 'Processing...',
          })
        }

        if (
          statusData.status === 'completed' ||
          statusData.status === 'complete'
        ) {
          return {
            id: statusData.id,
            url: statusData.url,
            file_size: statusData.file_size,
            duration: statusData.duration,
            metadata: {
              mimeType: statusData.metadata?.mimeType || 'audio/mpeg',
            },
          }
        }

        if (statusData.status === 'failed') {
          throw new Error(statusData.error || 'Upload failed')
        }

        // Wait 5 seconds before next check
        await new Promise(resolve => setTimeout(resolve, 5000))
        attempts++
      } catch (error) {
        console.error('Error polling upload status:', error)
        attempts++

        if (attempts >= maxAttempts) {
          throw new Error('Upload timeout - please try again')
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 5000))
      }
    }

    throw new Error('Upload timeout - please try again')
  }

  const uploadMultipleAudio = async (
    files: File[]
  ): Promise<AudioUploadResult[]> => {
    if (!session?.user?.id) {
      throw new Error('User not authenticated')
    }

    setIsUploading(true)
    const results: AudioUploadResult[] = []
    const totalFiles = files.length

    setBatchProgress({
      totalFiles,
      completedFiles: 0,
      currentFile: '',
      overallProgress: 0,
      fileProgresses: {},
    })

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const fileId = `file-${i}`

        setBatchProgress(prev =>
          prev
            ? {
                ...prev,
                currentFile: file.name,
              }
            : null
        )

        const onFileProgress = (progress: AudioUploadProgress) => {
          setBatchProgress(prev =>
            prev
              ? {
                  ...prev,
                  fileProgresses: {
                    ...prev.fileProgresses,
                    [fileId]: progress,
                  },
                  overallProgress: Math.round(
                    ((i + progress.progress / 100) / totalFiles) * 100
                  ),
                }
              : null
          )
        }

        try {
          const result = await uploadAudio(file, onFileProgress)
          results.push(result)

          setBatchProgress(prev =>
            prev
              ? {
                  ...prev,
                  completedFiles: prev.completedFiles + 1,
                  overallProgress: Math.round(((i + 1) / totalFiles) * 100),
                }
              : null
          )
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error)
          // Continue with other files
        }
      }

      return results
    } finally {
      setIsUploading(false)
      setBatchProgress(null)
    }
  }

  return {
    uploadAudio,
    uploadMultipleAudio,
    isUploading,
    uploadProgress,
    batchProgress,
  }
}
