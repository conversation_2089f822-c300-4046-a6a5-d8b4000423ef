import { useEffect, useState, useCallback } from 'react'
import { useVideoStore } from '@/store/video-store'
import { toast } from '@/lib/toast'

interface UseProjectDataResult {
  isLoading: boolean
  error: string | null
  projectLoaded: boolean
}

export function useProjectData(projectId: string | null): UseProjectDataResult {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [projectLoaded, setProjectLoaded] = useState(false)
  const { setProjectData, project } = useVideoStore()

  const fetchProjectData = useCallback(
    async (id: string) => {
      setIsLoading(true)
      setError(null)

      try {
        console.log(`[useProjectData] Fetching project ${id} from API`)
        const response = await fetch(`/api/projects/${id}`)

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch project data')
        }

        const projectData = await response.json()

        // Set project data in store (transformation will happen automatically)
        setProjectData(projectData)
        setProjectLoaded(true)
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error occurred'
        setError(errorMessage)
        toast.error(`Failed to load project: ${errorMessage}`)
        console.error('Error fetching project data:', err)
      } finally {
        setIsLoading(false)
      }
    },
    [setProjectData]
  )

  useEffect(() => {
    // If no projectId, skip
    if (!projectId) {
      setProjectLoaded(false)
      return
    }

    // If project already loaded for this ID, skip API call
    if (project && project.projectId === projectId) {
      console.log(
        `[useProjectData] Project ${projectId} already loaded in store, skipping API call`
      )
      setProjectLoaded(true)
      return
    }

    // Only fetch if we don't have the right project loaded
    console.log(
      `[useProjectData] Project ${projectId} not in store, fetching from API`
    )
    fetchProjectData(projectId)
  }, [projectId, project?.projectId, fetchProjectData])

  return {
    isLoading,
    error,
    projectLoaded,
  }
}
