export function createPasswordResetEmailTemplate(
  url: string,
  userName?: string
) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password - Adori AI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #000000 0%, #CC3333 50%, #000000 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #CC3333; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Password Reset Request</h1>
        </div>
        <div class="content">
          <h2>Hi${userName ? ` ${userName}` : ''},</h2>
          <p>We received a request to reset your password for your Adori AI account. Click the button below to create a new password:</p>

          <div style="text-align: center;">
            <a href="${url}" class="button">Reset Password</a>
          </div>

          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #CC3333;">${url}</p>

          <div class="warning">
            <strong>Security Notice:</strong> This link will expire in 1 hour for security reasons. If you didn't request a password reset, please ignore this email and your password will remain unchanged.
          </div>

          <p>If you didn't request this password reset, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>&copy; 2025 Adori AI. All rights reserved.</p>
          <p>This email was sent to you because you requested a password reset for your Adori AI account.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
Password Reset Request - Adori AI

Hi${userName ? ` ${userName}` : ''},

We received a request to reset your password for your Adori AI account. Click the link below to create a new password:

${url}

SECURITY NOTICE: This link will expire in 1 hour for security reasons. If you didn't request a password reset, please ignore this email and your password will remain unchanged.

If you didn't request this password reset, you can safely ignore this email.

Best regards,
The Adori AI Team
  `

  return { html, text }
}
