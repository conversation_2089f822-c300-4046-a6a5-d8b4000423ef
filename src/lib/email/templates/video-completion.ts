export function createVideoCompletionEmailTemplate({
  userName,
  videoName,
  projectId,
}: {
  userName?: string
  videoName: string
  projectId: string
}) {
  const projectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/scene-editor?projectId=${projectId}`
  const myVideosUrl = `${process.env.NEXT_PUBLIC_APP_URL}/my-videos`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Video is Ready! - Adori AI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #000000 0%, #CC3333 50%, #000000 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #CC3333; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .button-secondary { background-color: #CC3333 !important; }
        .button-container { text-align: center; margin: 30px 0; }
        .button-wrapper { margin: 15px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .video-info { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #CC3333; }
        .video-info h3 { margin: 0 0 10px 0; color: #CC3333; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎬 Your Video is Ready!</h1>
        </div>
        <div class="content">
          <h2>Hi${userName ? ` ${userName}` : ''},</h2>
          <p>Great news! Your video has been successfully exported and is ready for download.</p>

          <div class="video-info">
            <h3>📹 Video Details</h3>
            <p><strong>Video Name:</strong> ${videoName}</p>
            <p><strong>Status:</strong> ✅ Export completed successfully</p>
            <p><strong>Ready for:</strong> Download and sharing</p>
          </div>

          <p>You can access your video in two ways:</p>

          <div class="button-container">
            <div class="button-wrapper">

              <p style="font-size: 14px; color: #666; margin: 5px 0;">
                1. Open the project editor and check recent exports
              </p>
               <a href="${projectUrl}" class="button">
                📁 Go to Project
              </a>
            </div>

            <div class="button-wrapper">
            <p style="font-size: 14px; color: #666; margin: 5px 0;">
               2. View all your exported videos in one place
              </p>
              <a href="${myVideosUrl}" class="button">
                🎥 Go to My Videos
              </a>

            </div>
          </div>



          <div style="background: #e8f4fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #0066cc;">💡 What's Next?</h4>
            <ul style="margin: 0; padding-left: 20px;">
              <li>Download and share your video</li>
              <li>Create more videos with different content</li>
              <li>Upgrade to publish directly to Youtube</li>
              <li>Explore advanced editing features</li>
            </ul>
          </div>

          <p>Happy creating! 🚀</p>
        </div>
        <div class="footer">
          <p>&copy; 2025 Adori AI. All rights reserved.</p>
          <p>This email was sent because your video export completed successfully.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
Your Video is Ready! - Adori AI

Hi${userName ? ` ${userName}` : ''},

Great news! Your video has been successfully exported and is ready for download.

📹 Video Details:
- Video Name: ${videoName}
- Status: ✅ Export completed successfully
- Ready for: Download and sharing

You can access your video in two ways:

1. View in Project & Recent Exports:
   ${projectUrl}
   Open the project editor and check recent exports

2. Go to My Videos:
   ${myVideosUrl}
   View all your exported videos in one place



💡 What's Next?
• Download and share your video
• Create more videos with different content
• Upgrade to publish directly to Youtube
• Explore advanced editing features

Happy creating! 🚀

Best regards,
The Adori AI Team

© 2025 Adori AI. All rights reserved.
This email was sent because your video export completed successfully.
  `

  return { html, text }
}
