export function createVerificationEmailTemplate(
  url: string,
  userName?: string
) {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email - Adori AI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #000000 0%, #CC3333 50%, #000000 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .button { display: inline-block; background-color: #CC3333; color: white !important; padding: 14px 19px; text-decoration: none; border-radius: 8px; margin: 20px 0; text-align: center; box-sizing: border-box; }
        .button:link, .button:visited, .button:hover, .button:active { color: white !important; text-decoration: none; }
        .ii .button, .ii .button:link, .ii .button:visited, .ii .button:hover, .ii .button:active { color: white !important; }
        a.button, a.button:link, a.button:visited, a.button:hover, a.button:active { color: white !important; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to Adori AI!</h1>
        </div>
        <div class="content">
          <h2>Hi${userName ? ` ${userName}` : ''},</h2>
          <p>Thank you for signing up for Adori AI! To complete your registration, please verify your email address by clicking the button below:</p>

          <div style="text-align: center; color: #fff;">
            <a href="${url}" class="button">Verify Email Address</a>
          </div>

          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #CC3333;">${url}</p>

          <p>This link will expire in 24 hours for security reasons.</p>

          <p>If you didn't create an account with Adori AI, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>&copy; 2025 Adori AI. All rights reserved.</p>
          <p>This email was sent to you because you signed up for Adori AI.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
Welcome to Adori AI!

Hi${userName ? ` ${userName}` : ''},

Thank you for signing up for Adori AI! To complete your registration, please verify your email address by clicking the link below:

${url}

This link will expire in 24 hours for security reasons.

If you didn't create an account with Adori AI, you can safely ignore this email.

Best regards,
The Adori AI Team
  `

  return { html, text }
}
