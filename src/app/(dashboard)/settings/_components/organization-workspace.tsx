'use client'

import { authClient } from '@/lib/auth-client'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import {
  Copy,
  MoreHorizontal,
  Loader2,
  Building2,
  Users,
  Clock,
} from 'lucide-react'
import { toast } from 'sonner'
import { InviteMemberModal } from './invite-member-modal'
import { ChangeRoleModal } from './change-role-modal'
import { RemoveMemberModal } from './remove-member-modal'
import { z } from 'zod'

// Validation schema for organization name
const organizationNameSchema = z
  .string()
  .min(1, 'Workspace name is required')
  .max(30, 'Workspace name cannot exceed 30 characters')
  .trim()

interface Organization {
  id: string
  name: string
  slug: string
  logo?: string
  role: string
}

interface Member {
  id: string
  organizationId: string
  userId: string
  role: string
  createdAt: Date
  user: {
    id: string
    name: string
    email: string
    image: string | null | undefined
  }
}

interface Invitation {
  id: string
  organizationId: string
  email: string
  role: string
  status: string
  inviterId: string
  expiresAt: Date
  teamId?: string
}

export function OrganizationWorkspace() {
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [members, setMembers] = useState<Member[]>([])
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [tempOrgName, setTempOrgName] = useState('')
  const [originalOrgName, setOriginalOrgName] = useState('')
  const [inviting, setInviting] = useState(false)
  const [nameError, setNameError] = useState<string | null>(null)

  // Modal states
  const [changeRoleModal, setChangeRoleModal] = useState<{
    isOpen: boolean
    member: Member | null
  }>({ isOpen: false, member: null })
  const [removeMemberModal, setRemoveMemberModal] = useState<{
    isOpen: boolean
    member: Member | null
  }>({ isOpen: false, member: null })

  // Use the hook at the component level
  const { data: activeOrgData } = authClient.useActiveOrganization()
  const { data: session } = authClient.useSession()

  // Validation function
  const validateOrgName = (name: string) => {
    try {
      organizationNameSchema.parse(name)
      setNameError(null)
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        setNameError(error.errors[0].message)
      }
      return false
    }
  }

  // Handle organization name change with validation
  const handleOrgNameChange = (value: string) => {
    setTempOrgName(value)
    if (value.trim()) {
      validateOrgName(value)
    } else {
      setNameError(null)
    }
  }

  useEffect(() => {
    if (activeOrgData && activeOrgData.id) {
      // Convert Better Auth organization to our interface
      const org: Organization = {
        id: activeOrgData.id,
        name: activeOrgData.name,
        slug: activeOrgData.slug,
        logo: activeOrgData.logo || undefined,
        role: 'owner', // Default role for organization creator
      }
      setOrganization(org)
      setTempOrgName(org.name)
      setOriginalOrgName(org.name) // Set original name

      // Load members and invitations from the API response
      if (activeOrgData.members) {
        setMembers(activeOrgData.members)
      }
      if (activeOrgData.invitations) {
        setInvitations(activeOrgData.invitations)
      }
      setLoading(false)
    } else if (!loading) {
      // No organization found, create one
      createOrganization()
    }
  }, [activeOrgData, loading])

  const createOrganization = async () => {
    try {
      setLoading(true)
      const createResponse = await fetch('/api/create-user-organization', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (createResponse.ok) {
        const createData = await createResponse.json()
        if (createData.success && createData.organization) {
          const org: Organization = {
            id: createData.organization.id,
            name: createData.organization.name,
            slug: createData.organization.slug,
            logo: createData.organization.logo || undefined,
            role: 'owner',
          }
          setOrganization(org)
          setTempOrgName(org.name)
          setOriginalOrgName(org.name) // Set original name
          // Set empty members array since we'll load from API
          setMembers([])
          setInvitations([])
        }
      }
    } catch (error) {
      console.error('Failed to create organization:', error)
      toast.error('Failed to create workspace')
    } finally {
      setLoading(false)
    }
  }

  const handleCopySlug = async () => {
    if (!organization) return

    try {
      await navigator.clipboard.writeText(organization.slug)
      toast.success('Workspace slug copied to clipboard')
    } catch {
      toast.error('Failed to copy slug')
    }
  }

  const handleSaveWorkspace = async () => {
    if (!organization || !tempOrgName.trim()) return

    // Validate the organization name before saving
    if (!validateOrgName(tempOrgName)) {
      toast.error('Please fix the validation errors before saving')
      return
    }

    try {
      setUpdating(true)

      // Call the real API to update the organization
      await authClient.organization.update({
        organizationId: organization.id,
        data: {
          name: tempOrgName,
        },
      })

      // Update local state to reflect the change
      setOrganization({ ...organization, name: tempOrgName })
      setOriginalOrgName(tempOrgName) // Update original name
      setNameError(null) // Clear any validation errors

      toast.success('Workspace settings updated successfully')

      // Trigger a refresh of the useActiveOrganization hook by calling the API again
      // This ensures the sidebar updates with the new organization name
      try {
        // Call the organization API to refresh the hook data
        await authClient.organization.setActive({
          organizationId: organization.id,
        })
      } catch (error) {
        console.error('Failed to refresh organization data:', error)
      }
    } catch (error) {
      console.error('Failed to update workspace:', error)
      toast.error('Failed to update workspace settings')
    } finally {
      setUpdating(false)
    }
  }

  const handleInviteMember = async (email: string, role: string) => {
    if (!organization) return

    try {
      setInviting(true)

      // Use Better Auth organization invitation
      await authClient.organization.inviteMember({
        email,
        role: role as 'member' | 'admin' | 'owner',
        organizationId: organization.id,
      })

      // Refresh the organization data to get updated invitations
      if (activeOrgData) {
        // The hook will automatically refresh when the active organization changes
        console.log('Invitation sent successfully')
      }
    } catch (error) {
      console.error('Failed to invite member:', error)
      throw error // Re-throw to let the modal handle the error
    } finally {
      setInviting(false)
    }
  }

  const getRoleBadge = (role: string) => {
    const isAdmin = role === 'admin' || role === 'owner'
    return (
      <Badge variant={isAdmin ? 'default' : 'secondary'}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </Badge>
    )
  }

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      await authClient.organization.cancelInvitation({
        invitationId,
      })
      toast.success('Invitation canceled successfully')
      // Refresh invitations list
      if (activeOrgData) {
        // The hook will automatically refresh when the active organization changes
      }
    } catch (error) {
      console.error('Failed to cancel invitation:', error)
      toast.error('Failed to cancel invitation')
    }
  }

  const handleResendInvitation = async (invitationId: string) => {
    try {
      // For resending, we need to cancel the current invitation and create a new one
      const invitation = invitations.find(inv => inv.id === invitationId)
      if (!invitation) {
        toast.error('Invitation not found')
        return
      }

      await authClient.organization.cancelInvitation({
        invitationId,
      })

      await authClient.organization.inviteMember({
        email: invitation.email,
        role: invitation.role as 'member' | 'admin' | 'owner',
        organizationId: organization?.id,
        resend: true,
      })

      toast.success('Invitation resent successfully')
      // Refresh invitations list
      if (activeOrgData) {
        // The hook will automatically refresh when the active organization changes
      }
    } catch (error) {
      console.error('Failed to resend invitation:', error)
      toast.error('Failed to resend invitation')
    }
  }

  const handleChangeRole = (member: Member) => {
    if (!canModifyMember(member)) {
      const currentUserRole = getCurrentUserRole()

      if (isCurrentUser(member)) {
        toast.error('You cannot modify your own account')
      } else if (member.role === 'owner' && currentUserRole !== 'owner') {
        toast.error('Only owners can modify other owners')
      } else if (currentUserRole === 'member') {
        toast.error('Members cannot modify other users')
      } else if (currentUserRole === 'admin' && member.role !== 'member') {
        toast.error('Admins can only modify members')
      }
      return
    }
    setChangeRoleModal({ isOpen: true, member })
  }

  const handleRemoveMember = (member: Member) => {
    if (!canModifyMember(member)) {
      const currentUserRole = getCurrentUserRole()

      if (isCurrentUser(member)) {
        toast.error('You cannot modify your own account')
      } else if (member.role === 'owner' && currentUserRole !== 'owner') {
        toast.error('Only owners can modify other owners')
      } else if (currentUserRole === 'member') {
        toast.error('Members cannot modify other users')
      } else if (currentUserRole === 'admin' && member.role !== 'member') {
        toast.error('Admins can only modify members')
      }
      return
    }
    setRemoveMemberModal({ isOpen: true, member })
  }

  const handleRoleChangeSuccess = () => {
    // Refresh the members list
    if (activeOrgData) {
      // The hook will automatically refresh when the active organization changes
    }
  }

  const handleMemberRemoved = () => {
    // Refresh the members list
    if (activeOrgData) {
      // The hook will automatically refresh when the active organization changes
    }
  }

  const isCurrentUser = (member: Member) => {
    return session?.user?.id === member.userId
  }

  const getCurrentUserRole = () => {
    return members.find(member => isCurrentUser(member))?.role || 'member'
  }

  const canModifyMember = (targetMember: Member) => {
    const currentUserRole = getCurrentUserRole()

    // Users cannot modify themselves
    if (isCurrentUser(targetMember)) {
      return false
    }

    // Only owners can modify other owners
    if (targetMember.role === 'owner' && currentUserRole !== 'owner') {
      return false
    }

    // Admins can modify members but not other admins or owners
    if (currentUserRole === 'admin' && targetMember.role !== 'member') {
      return false
    }

    // Members cannot modify anyone
    if (currentUserRole === 'member') {
      return false
    }

    return true
  }

  if (loading) {
    return (
      <div className='flex items-center justify-center py-12'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <span className='ml-2'>Loading workspace...</span>
      </div>
    )
  }

  if (!organization) {
    return (
      <div className='text-center py-12'>
        <Building2 className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
        <h3 className='text-lg font-semibold mb-2'>No Active Workspace</h3>
        <p className='text-muted-foreground'>
          You need to be part of an organization to view workspace settings.
        </p>
      </div>
    )
  }

  return (
    <div className='space-y-6'>
      {/* Workspace Settings */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Building2 className='h-5 w-5' />
            Workspace Settings
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid gap-4'>
            <div>
              <label className='text-sm font-medium text-muted-foreground'>
                Workspace Name
              </label>
              <div className='flex gap-2 mt-1'>
                <Input
                  value={tempOrgName}
                  onChange={e => handleOrgNameChange(e.target.value)}
                  placeholder='Enter workspace name'
                />
                <Button
                  onClick={handleSaveWorkspace}
                  disabled={
                    updating ||
                    !tempOrgName.trim() ||
                    tempOrgName === originalOrgName ||
                    nameError !== null
                  }
                >
                  {updating ? (
                    <Loader2 className='h-4 w-4 animate-spin' />
                  ) : (
                    'Save'
                  )}
                </Button>
              </div>
              {nameError && (
                <p className='text-sm text-destructive mt-1'>{nameError}</p>
              )}
            </div>

            <div>
              <label className='text-sm font-medium text-muted-foreground'>
                Workspace Slug
              </label>
              <div className='flex gap-2 mt-1'>
                <Input
                  value={organization.slug}
                  readOnly
                  className='bg-muted'
                />
                <Button variant='outline' onClick={handleCopySlug}>
                  <Copy className='h-4 w-4' />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Members */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='flex items-center gap-2'>
              <Users className='h-5 w-5' />
              Team Members
            </CardTitle>
            <InviteMemberModal
              onInvite={handleInviteMember}
              loading={inviting}
              disabled={!organization}
            />
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue='members' className='w-full'>
            <TabsList className='border-b border-border flex pl-4 bg-transparent h-auto p-0'>
              <TabsTrigger
                value='members'
                className='py-2 sm:py-3 px-2 sm:px-4 mr-1 sm:mr-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1 whitespace-nowrap transition-all duration-200 data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:border-b-primary data-[state=inactive]:text-muted-foreground data-[state=inactive]:hover:text-foreground data-[state=inactive]:hover:bg-muted/40 bg-transparent relative'
              >
                Members
              </TabsTrigger>
              <TabsTrigger
                value='pending'
                className='py-2 sm:py-3 px-2 sm:px-4 mr-1 sm:mr-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1 whitespace-nowrap transition-all duration-200 data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:border-b-primary data-[state=inactive]:text-muted-foreground data-[state=inactive]:hover:text-foreground data-[state=inactive]:hover:bg-muted/40 bg-transparent relative'
              >
                Invitations
              </TabsTrigger>
            </TabsList>

            <TabsContent value='members' className='mt-4'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Actions</TableHead>
                    <TableHead className='w-[50px]'></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {members.map(member => (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div>
                          <div className='font-medium'>{member.user.email}</div>
                          {member.user.name && (
                            <div className='text-sm text-muted-foreground'>
                              {member.user.name}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{getRoleBadge(member.role)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant='ghost' size='sm'>
                              <MoreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem
                              onClick={() => handleChangeRole(member)}
                            >
                              Change Role
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className='text-red-600'
                              onClick={() => handleRemoveMember(member)}
                            >
                              Remove Member
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>

            <TabsContent value='pending' className='mt-4'>
              {invitations.length === 0 ? (
                <div className='text-center py-8'>
                  <Clock className='h-8 w-8 text-muted-foreground mx-auto mb-2' />
                  <p className='text-muted-foreground'>No invitations</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Expires at</TableHead>
                      <TableHead>Actions</TableHead>
                      <TableHead className='w-[50px]'></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invitations.map(invitation => (
                      <TableRow key={invitation.id}>
                        <TableCell>{invitation.email}</TableCell>
                        <TableCell>{getRoleBadge(invitation.role)}</TableCell>
                        <TableCell>
                          <Badge variant='outline'>{invitation.status}</Badge>
                        </TableCell>
                        <TableCell>
                          {invitation.expiresAt.toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' size='sm'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleResendInvitation(invitation.id)
                                }
                              >
                                Resend Invitation
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className='text-red-600'
                                onClick={() =>
                                  handleCancelInvitation(invitation.id)
                                }
                              >
                                Cancel Invitation
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Modals */}
      {changeRoleModal.member && (
        <ChangeRoleModal
          isOpen={changeRoleModal.isOpen}
          onClose={() => setChangeRoleModal({ isOpen: false, member: null })}
          member={changeRoleModal.member}
          organizationId={organization.id}
          onRoleChange={handleRoleChangeSuccess}
        />
      )}

      {removeMemberModal.member && (
        <RemoveMemberModal
          isOpen={removeMemberModal.isOpen}
          onClose={() => setRemoveMemberModal({ isOpen: false, member: null })}
          member={removeMemberModal.member}
          organizationId={organization.id}
          onMemberRemoved={handleMemberRemoved}
        />
      )}
    </div>
  )
}
