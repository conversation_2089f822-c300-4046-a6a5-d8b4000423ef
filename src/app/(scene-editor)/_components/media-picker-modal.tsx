'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogTitle } from '@/components/ui/dialog'
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer'
import {
  Check,
  Image as ImageIcon,
  Search,
  Wand2,
  Loader2,
  Eye,
  X,
  Globe,
  Lock,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

import {
  useStableMediaQuery,
  useInspectorDetection,
} from '@/hooks/use-stable-media-query'
// Removed unused tab and select imports
import {
  usePexelsVideos,
  usePexelsImages,
  PexelsVideo,
} from '@/hooks/usePexelsVideos'
import {
  usePixabayVideos,
  usePixabayImages,
  PixabayVideo,
} from '@/hooks/usePixabayVideos'
import { useUnsplashImages } from '@/hooks/useUnsplashImages'
import { useGiphyGifs, GiphyGif } from '@/hooks/useGiphyGifs'
import { useTenorGifs, TenorGif } from '@/hooks/useTenorGifs'

import { MediaGallery } from '@/components/media-gallery'
import { MediaUpload } from '@/components/media-upload'
import { StockPagination } from '@/components/stock-pagination'
import {
  type MediaAsset,
  useInvalidateMediaAssets,
} from '@/hooks/useMediaAssets'
import { toast } from '@/lib/toast'

// Define proper interfaces for API responses
interface PexelsImage {
  id: number
  photographer: string
  src: {
    medium: string
    large: string
  }
  width: number
  height: number
}

interface PixabayImage {
  id: number
  user: string
  webformatURL: string
  largeImageURL: string
  imageWidth: number
  imageHeight: number
}

interface UnsplashImage {
  id: string
  urls: {
    small: string
    regular: string
  }
  user?: {
    name: string
  }
  width: number
  height: number
}
import {
  useGenerateImageMutation,
  GenerateImageParams,
} from '@/hooks/useGenerateImageMutation'
import { useAIImageLimits } from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'

export interface Media {
  id: string | number
  type: 'video' | 'image' | 'gif' | 'sticker'
  title: string
  thumbnail: string
  width?: number
  height?: number
  videoUrl?: string
  user?: string
  duration?: string | number
  resolution?: string
  url?: string
  src?: string
  file?: File
}

interface MediaPickerModalProps {
  open: boolean
  onClose: () => void
  onSelect: (media: Media, applyToAll: boolean) => void
  project?: {
    blogImages?: string[]
  }
  filterMediaTypes?: ('image' | 'video' | 'gif' | 'sticker')[] // Optional filter for media types
}

const getImgModels = [
  { label: 'FLUX.1 Schnell', value: 'flux-1-schnell' },
  { label: 'Stable Diffusion XL', value: 'stable-diffusion-xl' },
]

// Simplified orientation options (only 3 orientations)
const orientations = [
  { label: 'Landscape', value: 'landscape' },
  { label: 'Portrait', value: 'portrait' },
  { label: 'Square', value: 'square' },
]

const styleTypes = [
  { label: 'Photorealism', value: 'photorealism' },
  { label: 'Anime', value: 'anime' },
  { label: 'Art', value: 'art' },
]

// Empty state component for better consistency
const EmptyState = ({
  message,
  icon = null,
  buttonText = '',
  onClick = () => {},
}: {
  message: string
  icon?: React.ReactNode
  buttonText?: string
  onClick?: () => void
}) => (
  <div className='min-h-[200px] flex flex-col items-center justify-center p-6 text-center'>
    <div className='rounded-full bg-muted/50 p-4 mb-4'>
      {icon || <ImageIcon className='h-10 w-10 text-muted-foreground/60' />}
    </div>
    <p className='text-muted-foreground mb-3 max-w-[300px]'>{message}</p>
    {buttonText && (
      <Button onClick={onClick} variant='outline' size='sm'>
        {buttonText}
      </Button>
    )}
  </div>
)

// Custom tab button component for more consistent styling
const TabButton = ({
  active,
  onClick,
  icon,
  label,
  disabled = false,
}: {
  active: boolean
  onClick: () => void
  icon: React.ReactNode | null
  label: string
  disabled?: boolean
}) => (
  <button
    className={`py-2 sm:py-3 px-2 sm:px-4 mr-1 sm:mr-2 text-xs sm:text-sm font-medium flex items-center justify-center gap-1 whitespace-nowrap transition-colors ${
      disabled
        ? 'text-muted-foreground/50 cursor-not-allowed'
        : active
          ? 'text-primary border-b-2 border-primary'
          : 'text-muted-foreground hover:text-foreground hover:bg-muted/40'
    }`}
    onClick={disabled ? undefined : onClick}
    disabled={disabled}
  >
    {icon}
    <span className='hidden sm:inline'>{label}</span>
    <span className='sm:hidden'>{label.split(' ')[0]}</span>
    {disabled && <Lock className='h-3 w-3 text-orange-500 ml-1' />}
  </button>
)

// Masonry Grid component for blog images
const MasonryGrid = ({
  images,
  onImageClick,
  selectedItems,
  onImagePreviewClick,
}: {
  images: string[]
  onImageClick: (imageUrl: string) => void
  selectedItems: (string | number)[]
  onImagePreviewClick?: (imageUrl: string) => void
}) => {
  if (!images || images.length === 0) {
    return (
      <div className='flex flex-col items-center justify-center h-64 text-center'>
        <ImageIcon className='h-12 w-12 mb-4 text-muted-foreground/40' />
        <p className='text-muted-foreground font-medium'>
          No blog images found
        </p>
        <p className='text-muted-foreground/60 text-sm mt-1'>
          Images from your blog content will appear here
        </p>
      </div>
    )
  }

  return (
    <div className='columns-1 sm:columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-4'>
      {images.map((imageUrl, idx) => (
        <div
          key={imageUrl}
          className={`break-inside-avoid mb-4 group relative rounded-lg overflow-hidden cursor-pointer border transition-all duration-200 hover:shadow-lg ${
            selectedItems.includes(imageUrl)
              ? 'border-primary ring-2 ring-primary/30'
              : 'border-border hover:border-primary/40'
          }`}
          onClick={() => onImageClick(imageUrl)}
        >
          <img
            src={imageUrl}
            alt={`Blog Image ${idx + 1}`}
            className='w-full h-auto object-cover transition-transform duration-200 hover:scale-[1.02]'
            loading='lazy'
            onError={e => {
              const img = e.target as HTMLImageElement
              img.style.display = 'none'
            }}
          />

          {/* Preview button */}
          {onImagePreviewClick && (
            <div className='absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity'>
              <div
                className='bg-black/70 text-white rounded-full p-1.5 hover:bg-black/90 transition-colors'
                onClick={e => {
                  e.stopPropagation()
                  onImagePreviewClick(imageUrl)
                }}
              >
                <Eye className='h-4 w-4' />
              </div>
            </div>
          )}

          {/* Selection indicator */}
          {selectedItems.includes(imageUrl) && (
            <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1'>
              <Check className='h-3 w-3' />
            </div>
          )}

          {/* Image overlay with gradient */}
          <div className='absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity' />
        </div>
      ))}
    </div>
  )
}

// Image Gallery component for consistent display
const ImageGallery = ({
  images,
  onImageClick,
  selectedImage,
  hoveredVideoId,
  setHoveredVideoId,
  onPreviewClick,
  onImagePreviewClick,
  videoOrientation = 'landscape',
}: {
  images: Media[]
  onImageClick: (id: string | number) => void
  selectedImage: string | null
  hoveredVideoId: string | number | null
  setHoveredVideoId: (id: string | number | null) => void
  onPreviewClick?: (videoUrl: string) => void
  onImagePreviewClick?: (imageUrl: string) => void
  videoOrientation?: 'landscape' | 'portrait' | 'square'
}) => {
  if (!images || images.length === 0) {
    return (
      <div className='flex flex-col items-center justify-center h-64 text-center'>
        <ImageIcon className='h-12 w-12 mb-4 text-muted-foreground/40' />
        <p className='text-muted-foreground font-medium'>No images found</p>
        <p className='text-muted-foreground/60 text-sm mt-1'>
          Try a different search term or category
        </p>
      </div>
    )
  }

  return (
    <div className='grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-4'>
      {images.map(image => (
        <div
          key={image.id}
          onClick={() => onImageClick(image.id)}
          onMouseEnter={() =>
            image.type === 'video' && setHoveredVideoId(image.id)
          }
          onMouseLeave={() => image.type === 'video' && setHoveredVideoId(null)}
          className={`relative overflow-hidden rounded-md border transition-all duration-200 hover:shadow-md cursor-pointer group ${
            selectedImage === image.id
              ? 'border-primary ring-2 ring-primary/30'
              : 'border-border hover:border-primary/40'
          }`}
          style={{
            aspectRatio:
              videoOrientation === 'portrait'
                ? '9/16'
                : videoOrientation === 'square'
                  ? '1/1'
                  : '16/9',
            background: 'var(--muted)',
          }}
        >
          {image.type === 'video' &&
          hoveredVideoId === image.id &&
          image.videoUrl ? (
            <video
              src={image.videoUrl}
              className='absolute top-0 left-0 w-full h-full object-cover'
              autoPlay
              muted
              loop
              playsInline
            />
          ) : (
            <img
              src={image.src || image.url || image.thumbnail}
              alt={image.title || 'Media'}
              className='absolute inset-0 w-full h-full object-cover'
            />
          )}
          {image.duration && (
            <div className='absolute bottom-2 left-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded'>
              {image.duration}
            </div>
          )}
          {image.width && image.height && (
            <div className='absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded'>
              {`${image.width}x${image.height}`}
            </div>
          )}
          {/* Preview buttons */}
          <div className='absolute top-2 left-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity'>
            {image.type === 'video' && image.videoUrl && onPreviewClick && (
              <div
                className='bg-black/70 text-white rounded-full p-1.5 hover:bg-black/90 transition-opacity'
                onClick={e => {
                  e.stopPropagation()
                  if (image.videoUrl) {
                    onPreviewClick(image.videoUrl)
                  }
                }}
              >
                <Eye className='h-4 w-4' />
              </div>
            )}
            {(image.type === 'image' || !image.type) && onImagePreviewClick && (
              <div
                className='bg-black/70 text-white rounded-full p-1.5 hover:bg-black/90 transition-opacity'
                onClick={e => {
                  e.stopPropagation()
                  const imageUrl = image.src || image.url || image.thumbnail
                  if (imageUrl) onImagePreviewClick(imageUrl)
                }}
              >
                <Eye className='h-4 w-4' />
              </div>
            )}
          </div>
          {selectedImage === image.id && (
            <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1'>
              <Check className='h-3 w-3' />
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

// Filter options component with setSelectedItems prop
const FilterOptions = ({
  mediaType,
  setMediaType,
  videoSource,
  setVideoSource,
  imageSource,
  setImageSource,
  gifSource,
  setGifSource,
  videoOrientation,
  setVideoOrientation,
  setSelectedItems,
  filterMediaTypes,
}: {
  mediaType: 'video' | 'image' | 'gif'
  setMediaType: (value: 'video' | 'image' | 'gif') => void
  videoSource: 'pexels' | 'pixabay'
  setVideoSource: (value: 'pexels' | 'pixabay') => void
  imageSource: 'pexels' | 'pixabay' | 'unsplash'
  setImageSource: (value: 'pexels' | 'pixabay' | 'unsplash') => void
  gifSource: 'giphy' | 'tenor'
  setGifSource: (value: 'giphy' | 'tenor') => void
  videoOrientation: 'landscape' | 'portrait' | 'square'
  setVideoOrientation: (value: 'landscape' | 'portrait' | 'square') => void
  setSelectedItems: (items: (number | string)[]) => void
  filterMediaTypes?: ('image' | 'video' | 'gif' | 'sticker')[]
}) => {
  // Determine which media types to show based on filter
  const allowedTypes = filterMediaTypes || ['video', 'image', 'gif']
  const showVideo = allowedTypes.includes('video')
  const showImage = allowedTypes.includes('image')
  const showGif = allowedTypes.includes('gif')

  return (
    <div className='grid gap-4 sm:grid-cols-3'>
      <div className='space-y-1 sm:space-y-2'>
        <Label className='text-xs sm:text-sm'>Media Type</Label>
        <div className='flex rounded-md overflow-hidden border divide-x'>
          {showVideo && (
            <Button
              type='button'
              variant={mediaType === 'video' ? 'default' : 'ghost'}
              className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                mediaType === 'video'
                  ? 'text-primary-foreground'
                  : 'text-muted-foreground'
              }`}
              onClick={() => {
                setMediaType('video')
                setSelectedItems([])
              }}
            >
              Video
            </Button>
          )}
          {showImage && (
            <Button
              type='button'
              variant={mediaType === 'image' ? 'default' : 'ghost'}
              className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                mediaType === 'image'
                  ? 'text-primary-foreground'
                  : 'text-muted-foreground'
              }`}
              onClick={() => {
                setMediaType('image')
                setSelectedItems([])
              }}
            >
              Image
            </Button>
          )}
          {showGif && (
            <Button
              type='button'
              variant={mediaType === 'gif' ? 'default' : 'ghost'}
              className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                mediaType === 'gif'
                  ? 'text-primary-foreground'
                  : 'text-muted-foreground'
              }`}
              onClick={() => {
                setMediaType('gif')
                setSelectedItems([])
              }}
            >
              GIF
            </Button>
          )}
        </div>
      </div>

      <div className='space-y-1 sm:space-y-2'>
        <Label className='text-xs sm:text-sm'>Source</Label>
        <div className='flex rounded-md overflow-hidden border divide-x'>
          {mediaType === 'video' ? (
            <>
              <Button
                type='button'
                variant={videoSource === 'pexels' ? 'default' : 'ghost'}
                className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                  videoSource === 'pexels'
                    ? 'text-primary-foreground'
                    : 'text-muted-foreground'
                }`}
                onClick={() => {
                  setVideoSource('pexels')
                  setSelectedItems([])
                }}
              >
                Pexels
              </Button>
              <Button
                type='button'
                variant={videoSource === 'pixabay' ? 'default' : 'ghost'}
                className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                  videoSource === 'pixabay'
                    ? 'text-primary-foreground'
                    : 'text-muted-foreground'
                }`}
                onClick={() => {
                  setVideoSource('pixabay')
                  setSelectedItems([])
                }}
              >
                Pixabay
              </Button>
            </>
          ) : mediaType === 'image' ? (
            <>
              <Button
                type='button'
                variant={imageSource === 'pexels' ? 'default' : 'ghost'}
                className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                  imageSource === 'pexels'
                    ? 'text-primary-foreground'
                    : 'text-muted-foreground'
                }`}
                onClick={() => {
                  setImageSource('pexels')
                  setSelectedItems([])
                }}
              >
                Pexels
              </Button>
              <Button
                type='button'
                variant={imageSource === 'pixabay' ? 'default' : 'ghost'}
                className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                  imageSource === 'pixabay'
                    ? 'text-primary-foreground'
                    : 'text-muted-foreground'
                }`}
                onClick={() => {
                  setImageSource('pixabay')
                  setSelectedItems([])
                }}
              >
                Pixabay
              </Button>
              <Button
                type='button'
                variant={imageSource === 'unsplash' ? 'default' : 'ghost'}
                className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                  imageSource === 'unsplash'
                    ? 'text-primary-foreground'
                    : 'text-muted-foreground'
                }`}
                onClick={() => {
                  setImageSource('unsplash')
                  setSelectedItems([])
                }}
              >
                Unsplash
              </Button>
            </>
          ) : (
            <>
              <Button
                type='button'
                variant={gifSource === 'giphy' ? 'default' : 'ghost'}
                className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                  gifSource === 'giphy'
                    ? 'text-primary-foreground'
                    : 'text-muted-foreground'
                }`}
                onClick={() => {
                  setGifSource('giphy')
                  setSelectedItems([])
                }}
              >
                Giphy
              </Button>
              <Button
                type='button'
                variant={gifSource === 'tenor' ? 'default' : 'ghost'}
                className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
                  gifSource === 'tenor'
                    ? 'text-primary-foreground'
                    : 'text-muted-foreground'
                }`}
                onClick={() => {
                  setGifSource('tenor')
                  setSelectedItems([])
                }}
              >
                Tenor
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Show orientation filter for both videos and images */}
      <div className='space-y-1 sm:space-y-2'>
        <Label className='text-xs sm:text-sm'>Orientation</Label>
        <div className='flex rounded-md overflow-hidden border divide-x'>
          <Button
            type='button'
            variant={videoOrientation === 'landscape' ? 'default' : 'ghost'}
            className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
              videoOrientation === 'landscape'
                ? 'text-primary-foreground'
                : 'text-muted-foreground'
            }`}
            onClick={() => {
              setVideoOrientation('landscape')
              setSelectedItems([])
            }}
          >
            Landscape
          </Button>
          <Button
            type='button'
            variant={videoOrientation === 'portrait' ? 'default' : 'ghost'}
            className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
              videoOrientation === 'portrait'
                ? 'text-primary-foreground'
                : 'text-muted-foreground'
            }`}
            onClick={() => {
              setVideoOrientation('portrait')
              setSelectedItems([])
            }}
          >
            Portrait
          </Button>
          <Button
            type='button'
            variant={videoOrientation === 'square' ? 'default' : 'ghost'}
            className={`flex-1 rounded-none px-2 sm:px-3 h-8 ${
              videoOrientation === 'square'
                ? 'text-primary-foreground'
                : 'text-muted-foreground'
            }`}
            onClick={() => {
              setVideoOrientation('square')
              setSelectedItems([])
            }}
          >
            Square
          </Button>
        </div>
      </div>
    </div>
  )
}

// Image mapping function for Pexels
const mapPexelsImages = (images: PexelsImage[]): Media[] => {
  return images.map(image => ({
    id: image.id,
    type: 'image' as const,
    title: image.photographer || 'Pexels Image',
    src: image.src.large,
    thumbnail: image.src.medium,
    width: image.width,
    height: image.height,
    user: image.photographer,
  }))
}

// Image mapping function for Pixabay
const mapPixabayImages = (images: PixabayImage[]): Media[] => {
  return images.map(image => ({
    id: image.id,
    type: 'image' as const,
    title: image.user || 'Pixabay Image',
    src: image.largeImageURL,
    thumbnail: image.webformatURL,
    width: image.imageWidth,
    height: image.imageHeight,
    user: image.user,
  }))
}

// Image mapping function for Unsplash
const mapUnsplashImages = (images: UnsplashImage[]): Media[] => {
  return images.map(image => ({
    id: image.id,
    type: 'image' as const,
    title: image.user?.name || 'Unsplash Image',
    src: image.urls.regular,
    thumbnail: image.urls.small,
    width: image.width,
    height: image.height,
    user: image.user?.name || '',
  }))
}

export function MediaPickerModal({
  open,
  onClose,
  onSelect,
  project,
  filterMediaTypes,
}: MediaPickerModalProps) {
  const [activeTab, setActiveTab] = useState('stock')

  // Set default media type based on filter
  const getDefaultMediaType = (): 'video' | 'image' | 'gif' => {
    if (filterMediaTypes) {
      if (filterMediaTypes.includes('image')) return 'image'
      if (filterMediaTypes.includes('video')) return 'video'
      if (filterMediaTypes.includes('gif')) return 'gif'
    }
    return 'video' // Default fallback
  }

  const [mediaType, setMediaType] = useState<'video' | 'image' | 'gif'>(
    getDefaultMediaType()
  )
  const [videoSource, setVideoSource] = useState<'pexels' | 'pixabay'>('pexels')
  const [imageSource, setImageSource] = useState<
    'pexels' | 'pixabay' | 'unsplash'
  >('pexels')
  const [gifSource, setGifSource] = useState<'giphy' | 'tenor'>('giphy')
  const [selectedItems, setSelectedItems] = useState<(number | string)[]>([])
  const [applyToAll] = useState(false)
  const [myMedia, setMyMedia] = useState<Media[]>([])
  const [myMediaTab, setMyMediaTab] = useState<'upload' | 'gallery'>('upload')
  const [myMediaType] = useState<'all' | 'image' | 'video'>('all')
  const [myMediaSearch, setMyMediaSearch] = useState('')
  const [myMediaSearchInput] = useState('')
  const [selectedMedia, setSelectedMedia] = useState<MediaAsset | null>(null)
  const invalidateMediaAssets = useInvalidateMediaAssets()
  // Upload handlers for media tabs
  const handleUploadComplete = (result: unknown) => {
    console.log('Upload completed:', result)
    toast.success('Media uploaded successfully!')
    // Refresh the gallery
    invalidateMediaAssets()
    // Switch to gallery tab to see uploaded media
    setMyMediaTab('gallery')
  }

  const handleUploadError = (error: Error) => {
    console.error('Upload error:', error)
    toast.error(`Upload failed: ${error.message}`)
  }

  const handleMediaSelect = (media: MediaAsset) => {
    setSelectedMedia(media)
    setSelectedItems([media.id])
    console.log('Selected media:', media)
  }
  const [searchInput, setSearchInput] = useState('nature')
  const [search, setSearch] = useState('nature')
  const [videoOrientation, setVideoOrientation] = useState<
    'landscape' | 'portrait' | 'square'
  >('landscape')

  // Pagination state for stock libraries
  const [pexelsVideoPage, setPexelsVideoPage] = useState(1)
  const [pixabayVideoPage, setPixabayVideoPage] = useState(1)
  const [pexelsImagePage, setPexelsImagePage] = useState(1)
  const [pixabayImagePage, setPixabayImagePage] = useState(1)
  const [unsplashImagePage, setUnsplashImagePage] = useState(1)
  const [giphyGifPage, setGiphyGifPage] = useState(1)
  const [tenorGifPage, setTenorGifPage] = useState(1)

  // Reset pagination when search or filters change
  useEffect(() => {
    setPexelsVideoPage(1)
    setPixabayVideoPage(1)
    setPexelsImagePage(1)
    setPixabayImagePage(1)
    setUnsplashImagePage(1)
    setGiphyGifPage(1)
    setTenorGifPage(1)
  }, [search, videoOrientation, mediaType, videoSource, imageSource, gifSource])

  // Debounce search input for stock media
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearch(searchInput)
    }, 500) // 500ms delay

    return () => clearTimeout(timer)
  }, [searchInput])

  // Debounce search input for My Media
  useEffect(() => {
    const timer = setTimeout(() => {
      setMyMediaSearch(myMediaSearchInput.trim())
    }, 500) // 500ms delay

    return () => clearTimeout(timer)
  }, [myMediaSearchInput])

  // Video preview state
  const [hoveredVideoId, setHoveredVideoId] = useState<string | number | null>(
    null
  )
  const [previewVideoUrl, setPreviewVideoUrl] = useState<string | null>(null)
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null)

  // Generate tab state

  const [generatePrompt, setGeneratePrompt] = useState('')
  const [generateModel, setGenerateModel] = useState(getImgModels[0].value)
  const [generatedImage, setGeneratedImage] = useState<string | null>(null)
  const [generateOrientation, setGenerateOrientation] = useState(
    orientations[0].value
  )
  const [generateStyle, setGenerateStyle] = useState(styleTypes[0].value)

  // API queries
  /* eslint-disable @typescript-eslint/no-unused-vars */
  const {
    data: pexelsData,
    isLoading: pexelsLoading,
    isError: pexelsError,
    refetch: refetchPexels,
  } = usePexelsVideos(
    search,
    activeTab === 'stock' && mediaType === 'video' && videoSource === 'pexels',
    videoOrientation,
    pexelsVideoPage
  )

  const {
    data: pixabayData,
    isLoading: pixabayLoading,
    isError: pixabayError,
  } = usePixabayVideos(
    search,
    activeTab === 'stock' && mediaType === 'video' && videoSource === 'pixabay',
    videoOrientation,
    pixabayVideoPage
  )

  const {
    data: pexelsImagesData,
    isLoading: pexelsImagesLoading,
    isError: pexelsImagesError,
  } = usePexelsImages(
    search,
    activeTab === 'stock' && mediaType === 'image' && imageSource === 'pexels',
    videoOrientation,
    pexelsImagePage
  )

  const {
    data: pixabayImagesData,
    isLoading: pixabayImagesLoading,
    isError: pixabayImagesError,
  } = usePixabayImages(
    search,
    activeTab === 'stock' && mediaType === 'image' && imageSource === 'pixabay',
    videoOrientation,
    pixabayImagePage
  )

  const {
    data: unsplashImagesData,
    isLoading: unsplashImagesLoading,
    isError: unsplashImagesError,
  } = useUnsplashImages(
    search,
    activeTab === 'stock' &&
      mediaType === 'image' &&
      imageSource === 'unsplash',
    videoOrientation,
    unsplashImagePage
  )

  const {
    data: giphyGifsData,
    isLoading: giphyGifsLoading,
    isError: giphyGifsError,
  } = useGiphyGifs(
    search,
    activeTab === 'stock' && mediaType === 'gif' && gifSource === 'giphy',
    videoOrientation,
    giphyGifPage
  )

  const {
    data: tenorGifsData,
    isLoading: tenorGifsLoading,
    isError: tenorGifsError,
  } = useTenorGifs(
    search,
    activeTab === 'stock' && mediaType === 'gif' && gifSource === 'tenor',
    videoOrientation,
    tenorGifPage
  )

  // Generate tab params
  const style =
    styleTypes.find(s => s.value === generateStyle)?.value ||
    styleTypes[0].value

  const generateParams: GenerateImageParams = {
    model: generateModel,
    prompt: style ? `${generatePrompt}, ${style}` : generatePrompt,
    orientation: generateOrientation,
    style,
  }

  const generateImageMutation = useGenerateImageMutation()
  const aiImageLimits = useAIImageLimits()
  const { openUpgradeModal } = useUpgradeModal()

  const generateLoading = generateImageMutation.isPending
  const generateError = generateImageMutation.error

  // Handle AI image generation with limits check
  const handleGenerateImage = () => {
    if (!aiImageLimits.allowed) {
      openUpgradeModal('aiImages', aiImageLimits.upgradeMessage)
      return
    }
    generateImageMutation.mutate(generateParams)
  }

  // Handle successful generation
  useEffect(() => {
    if (generateImageMutation.data) {
      const data = generateImageMutation.data
      let imageUrl: string | null = null
      if ('imageUrl' in data) {
        imageUrl = data.imageUrl
      }
      setGeneratedImage(imageUrl)
    }
  }, [generateImageMutation.data])

  const filteredMyMedia = myMedia.filter(item =>
    item.title.toLowerCase().includes(myMediaSearch.toLowerCase())
  )

  const handleSelect = () => {
    let selected: Media | undefined

    if (activeTab === 'my') {
      // For the new tab structure, we only handle gallery selections
      if (myMediaTab === 'gallery' && selectedMedia) {
        // Convert MediaAsset to Media format for compatibility
        const isVideo = selectedMedia.mimeType.startsWith('video/')
        selected = {
          id: selectedMedia.id,
          type: isVideo ? 'video' : 'image',
          title: selectedMedia.originalName,
          thumbnail: selectedMedia.thumbnailUrl || '',
          url: selectedMedia.originalUrl,
          videoUrl: isVideo ? selectedMedia.originalUrl : undefined,
          duration: selectedMedia.duration || undefined,
          resolution:
            selectedMedia.width && selectedMedia.height
              ? `${selectedMedia.width}x${selectedMedia.height}`
              : undefined,
        } as Media
      }
    } else if (activeTab === 'stock' && mediaType === 'video') {
      if (videoSource === 'pexels' && pexelsData?.videos) {
        const v = pexelsData.videos.find(
          (v: PexelsVideo) => v.id === selectedItems[0]
        )
        if (v) {
          selected = {
            id: v.id,
            type: 'video',
            title: v.user?.name || 'Pexels Video',
            thumbnail: v.image,
            duration: v.duration ? v.duration : '',
            resolution:
              v.video_files?.[0]?.width && v.video_files?.[0]?.height
                ? `${v.video_files[0].width}x${v.video_files[0].height}`
                : '',
            videoUrl:
              v.video_files?.find(
                (f: { quality: string }) => f.quality === 'hd'
              )?.link || v.video_files?.[0]?.link,
          }
        }
      } else if (videoSource === 'pixabay' && pixabayData?.hits) {
        const v = pixabayData.hits.find(
          (v: PixabayVideo) => v.id === selectedItems[0]
        )
        if (v) {
          const bestVideo =
            v.videos.large || v.videos.medium || v.videos.small || v.videos.tiny
          const tinyThumb = v.videos.tiny?.url
            ? v.videos.tiny.url.replace('.mp4', '.jpg')
            : ''
          const smallThumb = v.videos.small?.url
            ? v.videos.small.url.replace('.mp4', '.jpg')
            : ''
          const mediumThumb = v.videos.medium?.url
            ? v.videos.medium.url.replace('.mp4', '.jpg')
            : ''
          const largeThumb = v.videos.large?.url
            ? v.videos.large.url.replace('.mp4', '.jpg')
            : ''
          const thumbnail = tinyThumb || smallThumb || mediumThumb || largeThumb
          selected = {
            id: v.id,
            type: 'video',
            title: v.user || 'Pixabay Video',
            thumbnail,
            duration: v.duration ? v.duration : '',
            resolution:
              bestVideo?.width && bestVideo?.height
                ? `${bestVideo.width}x${bestVideo.height}`
                : '',
            videoUrl: bestVideo?.url,
          }
        }
      }
    } else if (activeTab === 'stock' && mediaType === 'image') {
      if (imageSource === 'pexels' && pexelsImagesData?.photos) {
        const img = pexelsImagesData.photos.find(
          (img: PexelsImage) => img.id === selectedItems[0]
        )
        if (img) {
          selected = {
            id: img.id,
            type: 'image',
            title: img.alt || 'Pexels Image',
            thumbnail: img.src.large,
            url: img.src.large,
          }
        }
      } else if (imageSource === 'pixabay' && pixabayImagesData?.hits) {
        const img = pixabayImagesData.hits.find(
          (img: PixabayImage) => img.id === selectedItems[0]
        )
        if (img) {
          selected = {
            id: img.id,
            type: 'image',
            title: img.tags || 'Pixabay Image',
            thumbnail: img.webformatURL,
            url: img.largeImageURL,
          }
        }
      } else if (imageSource === 'unsplash' && unsplashImagesData?.results) {
        const img = unsplashImagesData.results.find(
          (img: UnsplashImage) => img.id === String(selectedItems[0])
        )
        if (img) {
          selected = {
            id: img.id,
            type: 'image',
            title: img.alt_description || 'Unsplash Image',
            thumbnail: img.urls.regular,
            url: img.urls.regular,
          }
        }
      }
    } else if (activeTab === 'stock' && mediaType === 'gif') {
      if (gifSource === 'giphy' && giphyGifsData?.data) {
        const gif = giphyGifsData.data.find(
          (gif: GiphyGif) => gif.id === selectedItems[0]
        )
        if (gif) {
          selected = {
            id: gif.id,
            type: 'gif',
            title: gif.title || 'Giphy GIF',
            thumbnail: gif.images.fixed_height.url,
            url: gif.images.original.url,
            width: parseInt(gif.images.original.width),
            height: parseInt(gif.images.original.height),
          }
        }
      } else if (gifSource === 'tenor' && tenorGifsData?.results) {
        const gif = tenorGifsData.results.find(
          (gif: TenorGif) => gif.id === selectedItems[0]
        )
        if (gif) {
          const gifMedia = gif.media_formats?.gif
          selected = {
            id: gif.id,
            type: 'gif',
            title: gif.title || 'Tenor GIF',
            thumbnail: gif.media_formats?.tinygif?.url || gifMedia?.url || '',
            url: gifMedia?.url || '',
            width: gifMedia?.dims?.[0],
            height: gifMedia?.dims?.[1],
          }
        }
      }
    } else if (activeTab === 'blog' && project?.blogImages) {
      // Handle blog images selection
      const imageUrl = selectedItems[0] as string
      if (project.blogImages.includes(imageUrl)) {
        selected = {
          id: imageUrl,
          type: 'image',
          title: 'Blog Image',
          thumbnail: imageUrl,
          url: imageUrl,
        }
      }
    }

    if (selected) {
      onSelect(selected, applyToAll)
    }
    onClose()
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    const url = URL.createObjectURL(file)
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')
    const type: Media['type'] = isImage ? 'image' : isVideo ? 'video' : 'image'
    const newMedia: Media = {
      id: Date.now().toString(),
      type,
      title: file.name,
      thumbnail: url,
      url,
      file,
    }
    setMyMedia(prev => [newMedia, ...prev])
    setSelectedItems([newMedia.id])
  }

  const handleSearch = () => {
    setSearch(searchInput)
  }

  const handleMyMediaSearch = () => {
    // The debounced effect will handle setting myMediaSearch
    // This function can be used for immediate search if needed
    setMyMediaSearch(myMediaSearchInput.trim())
  }

  // Check if mobile view with stable detection
  const isMobile = useStableMediaQuery('(max-width: 768px)')
  const isInspectorOpen = useInspectorDetection()

  // Prevent layout shifts when inspector is opened
  useEffect(() => {
    if (isInspectorOpen) {
      // Add a small delay when inspector state changes to prevent layout thrashing
      const timeoutId = setTimeout(() => {
        // Layout stabilization
      }, 300)

      return () => clearTimeout(timeoutId)
    }
  }, [isInspectorOpen])

  // Unified pagination component - positioned above footer
  const renderPagination = () => {
    if (activeTab !== 'stock') return null

    // Video pagination
    if (mediaType === 'video') {
      if (videoSource === 'pexels' && pexelsData?.total_results) {
        return (
          <div className='border-t bg-background py-3 px-4 flex-shrink-0'>
            <StockPagination
              currentPage={pexelsVideoPage}
              totalPages={Math.ceil(pexelsData.total_results / 24)}
              onPageChange={setPexelsVideoPage}
            />
          </div>
        )
      }
      if (videoSource === 'pixabay' && pixabayData?.total) {
        return (
          <div className='border-t bg-background py-3 px-4 flex-shrink-0'>
            <StockPagination
              currentPage={pixabayVideoPage}
              totalPages={Math.ceil(pixabayData.total / 24)}
              onPageChange={setPixabayVideoPage}
            />
          </div>
        )
      }
    }

    // Image pagination
    if (mediaType === 'image') {
      if (imageSource === 'pexels' && pexelsImagesData?.total_results) {
        return (
          <div className='border-t bg-background py-3 px-4 flex-shrink-0'>
            <StockPagination
              currentPage={pexelsImagePage}
              totalPages={Math.ceil(pexelsImagesData.total_results / 24)}
              onPageChange={setPexelsImagePage}
            />
          </div>
        )
      }
      if (imageSource === 'pixabay' && pixabayImagesData?.total) {
        return (
          <div className='border-t bg-background py-3 px-4 flex-shrink-0'>
            <StockPagination
              currentPage={pixabayImagePage}
              totalPages={Math.ceil(pixabayImagesData.total / 24)}
              onPageChange={setPixabayImagePage}
            />
          </div>
        )
      }
      if (imageSource === 'unsplash' && unsplashImagesData?.total_pages) {
        return (
          <div className='border-t bg-background py-3 px-4 flex-shrink-0'>
            <StockPagination
              currentPage={unsplashImagePage}
              totalPages={unsplashImagesData.total_pages}
              onPageChange={setUnsplashImagePage}
            />
          </div>
        )
      }
    }

    // GIF pagination
    if (mediaType === 'gif') {
      if (gifSource === 'giphy' && giphyGifsData?.pagination?.total_count) {
        return (
          <div className='border-t bg-background py-3 px-4 flex-shrink-0'>
            <StockPagination
              currentPage={giphyGifPage}
              totalPages={Math.ceil(giphyGifsData.pagination.total_count / 24)}
              onPageChange={setGiphyGifPage}
            />
          </div>
        )
      }
      if (gifSource === 'tenor' && tenorGifsData?.next) {
        return (
          <div className='border-t bg-background py-3 px-4 flex-shrink-0'>
            <StockPagination
              currentPage={tenorGifPage}
              totalPages={tenorGifPage + 1} // Tenor doesn't provide total, so show next page
              onPageChange={setTenorGifPage}
            />
          </div>
        )
      }
    }

    return null
  }

  // Main content component
  const renderContent = () => (
    <div className='grid grid-rows-[auto_1fr_auto_auto] h-full min-h-0'>
      {/* Tab navigation */}
      <div className='border-b border-border flex pl-4'>
        <div className='flex'>
          <TabButton
            active={activeTab === 'stock'}
            onClick={() => {
              setActiveTab('stock')
              setSelectedItems([])
            }}
            icon={null}
            label='Stock'
          />
          <TabButton
            active={activeTab === 'my'}
            onClick={() => {
              setActiveTab('my')
              setSelectedItems([])
            }}
            icon={<ImageIcon className='h-4 w-4' />}
            label='My Media'
          />
          <TabButton
            active={activeTab === 'generate'}
            onClick={() => {
              if (!aiImageLimits.allowed) {
                openUpgradeModal('aiImages', aiImageLimits.upgradeMessage)
                return
              }
              setActiveTab('generate')
            }}
            icon={<Wand2 className='h-4 w-4' />}
            label='Generate'
            disabled={!aiImageLimits.allowed}
          />
          {/* Imported from Blog Tab (only show if blogImages exist) */}
          {project?.blogImages && project.blogImages.length > 0 && (
            <TabButton
              active={activeTab === 'blog'}
              onClick={() => {
                setActiveTab('blog')
                setSelectedItems([])
              }}
              icon={<Globe className='h-4 w-4' />}
              label='Imported Assets'
            />
          )}
        </div>
      </div>

      <div className='overflow-y-auto hide-scrollbar min-h-0'>
        {/* Stock Tab */}
        {activeTab === 'stock' && (
          <>
            {/* Search bar */}
            <div className='p-4 border-b border-border'>
              <div className='flex gap-2'>
                <div className='relative flex-1'>
                  <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                  <Input
                    value={searchInput}
                    onChange={e => setSearchInput(e.target.value)}
                    placeholder='Search media...'
                    className='pl-9 w-full'
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        handleSearch()
                      }
                    }}
                  />
                  {searchInput && (
                    <X
                      className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground'
                      onClick={() => setSearchInput('')}
                    />
                  )}
                </div>
                <Button
                  onClick={handleSearch}
                  variant='secondary'
                  className='shrink-0'
                >
                  Search
                </Button>
              </div>
            </div>

            {/* Filters */}
            <div className='p-2 border-b border-border'>
              <FilterOptions
                mediaType={mediaType}
                setMediaType={setMediaType}
                videoSource={videoSource}
                setVideoSource={setVideoSource}
                imageSource={imageSource}
                setImageSource={setImageSource}
                gifSource={gifSource}
                setGifSource={setGifSource}
                videoOrientation={videoOrientation}
                setVideoOrientation={setVideoOrientation}
                setSelectedItems={setSelectedItems}
                filterMediaTypes={filterMediaTypes}
              />
            </div>

            {/* Results grid */}
            <div className='p-4 overflow-auto max-h-[45vh]'>
              {(mediaType === 'video' &&
                videoSource === 'pexels' &&
                pexelsLoading) ||
              (mediaType === 'video' &&
                videoSource === 'pixabay' &&
                pixabayLoading) ||
              (mediaType === 'image' &&
                imageSource === 'pexels' &&
                pexelsImagesLoading) ||
              (mediaType === 'image' &&
                imageSource === 'pixabay' &&
                pixabayImagesLoading) ||
              (mediaType === 'image' &&
                imageSource === 'unsplash' &&
                unsplashImagesLoading) ||
              (mediaType === 'gif' &&
                gifSource === 'giphy' &&
                giphyGifsLoading) ||
              (mediaType === 'gif' &&
                gifSource === 'tenor' &&
                tenorGifsLoading) ? (
                <div className='flex items-center justify-center h-64'>
                  <Loader2 className='h-8 w-8 animate-spin text-primary' />
                </div>
              ) : (
                <>
                  {mediaType === 'video' &&
                    videoSource === 'pexels' &&
                    (pexelsData?.videos?.length ? (
                      <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4'>
                        {pexelsData.videos.map((v: PexelsVideo) => {
                          const item = {
                            id: v.id,
                            type: 'video',
                            title: v.user?.name || 'Pexels Video',
                            thumbnail: v.image,
                            duration: v.duration
                              ? `${Math.floor(v.duration / 60)}:${(
                                  v.duration % 60
                                )
                                  .toString()
                                  .padStart(2, '0')}`
                              : '',
                            resolution:
                              v.video_files?.[0]?.width &&
                              v.video_files?.[0]?.height
                                ? `${v.video_files[0].width}x${v.video_files[0].height}`
                                : '',
                            videoUrl:
                              v.video_files?.find(
                                (f: { quality: string }) => f.quality === 'hd'
                              )?.link || v.video_files?.[0]?.link,
                          }
                          return (
                            <div
                              key={item.id}
                              className={`group relative rounded-lg overflow-hidden cursor-pointer border transition-all duration-200 ${
                                selectedItems.includes(item.id)
                                  ? 'border-primary ring-2 ring-primary/30'
                                  : 'border-border hover:border-primary/40'
                              }`}
                              onClick={() => setSelectedItems([item.id])}
                              onMouseEnter={() => setHoveredVideoId(item.id)}
                              onMouseLeave={() => setHoveredVideoId(null)}
                              style={{
                                aspectRatio:
                                  videoOrientation === 'portrait'
                                    ? '9/16'
                                    : videoOrientation === 'square'
                                      ? '1/1'
                                      : '16/9',
                                background: 'var(--muted)',
                              }}
                            >
                              {hoveredVideoId === item.id && item.videoUrl ? (
                                <video
                                  src={item.videoUrl}
                                  className='absolute top-0 left-0 w-full h-full object-cover'
                                  autoPlay
                                  muted
                                  loop
                                  playsInline
                                />
                              ) : (
                                <img
                                  src={item.thumbnail}
                                  alt={item.title}
                                  className='absolute top-0 left-0 w-full h-full object-cover'
                                />
                              )}
                              <div className='absolute bottom-2 left-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded'>
                                {item.duration}
                              </div>
                              <div className='absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded'>
                                {item.resolution}
                              </div>
                              {item.videoUrl && (
                                <div
                                  className='absolute top-2 left-2 bg-black/70 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 hover:bg-black/90 transition-opacity'
                                  onClick={e => {
                                    e.stopPropagation()
                                    if (item.videoUrl) {
                                      setPreviewVideoUrl(item.videoUrl)
                                    }
                                  }}
                                >
                                  <Eye className='h-4 w-4' />
                                </div>
                              )}
                              {selectedItems.includes(item.id) && (
                                <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1'>
                                  <Check className='h-3 w-3' />
                                </div>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    ) : (
                      <EmptyState
                        message='No videos found'
                        icon={
                          <ImageIcon className='h-10 w-10 text-muted-foreground/60' />
                        }
                      />
                    ))}

                  {mediaType === 'video' &&
                    videoSource === 'pixabay' &&
                    (pixabayData?.hits?.length ? (
                      <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4'>
                        {pixabayData.hits.map((v: PixabayVideo) => {
                          const bestVideo =
                            v.videos.large ||
                            v.videos.medium ||
                            v.videos.small ||
                            v.videos.tiny
                          const thumbnail = v.videos.tiny?.url
                            ? v.videos.tiny.url.replace('.mp4', '.jpg')
                            : ''
                          return (
                            <div
                              key={v.id}
                              className={`group relative rounded-lg overflow-hidden cursor-pointer border transition-all duration-200 ${
                                selectedItems.includes(v.id)
                                  ? 'border-primary ring-2 ring-primary/30'
                                  : 'border-border hover:border-primary/40'
                              }`}
                              onClick={() => setSelectedItems([v.id])}
                              onMouseEnter={() => setHoveredVideoId(v.id)}
                              onMouseLeave={() => setHoveredVideoId(null)}
                              style={{
                                aspectRatio:
                                  videoOrientation === 'portrait'
                                    ? '9/16'
                                    : videoOrientation === 'square'
                                      ? '1/1'
                                      : '16/9',
                                background: 'var(--muted)',
                              }}
                            >
                              {hoveredVideoId === v.id && bestVideo?.url ? (
                                <video
                                  src={bestVideo.url}
                                  className='absolute top-0 left-0 w-full h-full object-cover'
                                  autoPlay
                                  muted
                                  loop
                                  playsInline
                                />
                              ) : (
                                <img
                                  src={thumbnail}
                                  alt={v.user || 'Pixabay Video'}
                                  className='absolute top-0 left-0 w-full h-full object-cover'
                                />
                              )}
                              <div className='absolute bottom-2 left-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded'>
                                {v.duration
                                  ? `${Math.floor(v.duration / 60)}:${(v.duration % 60).toString().padStart(2, '0')}`
                                  : ''}
                              </div>
                              <div className='absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded'>
                                {bestVideo?.width && bestVideo?.height
                                  ? `${bestVideo.width}x${bestVideo.height}`
                                  : ''}
                              </div>
                              {bestVideo?.url && (
                                <div
                                  className='absolute top-2 left-2 bg-black/70 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 hover:bg-black/90 transition-opacity'
                                  onClick={e => {
                                    e.stopPropagation()
                                    if (bestVideo?.url) {
                                      setPreviewVideoUrl(bestVideo.url)
                                    }
                                  }}
                                >
                                  <Eye className='h-4 w-4' />
                                </div>
                              )}
                              {selectedItems.includes(v.id) && (
                                <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1'>
                                  <Check className='h-3 w-3' />
                                </div>
                              )}
                            </div>
                          )
                        })}
                      </div>
                    ) : (
                      <EmptyState
                        message='No videos found'
                        icon={
                          <ImageIcon className='h-10 w-10 text-muted-foreground/60' />
                        }
                      />
                    ))}

                  {mediaType === 'image' && (
                    <>
                      {imageSource === 'pexels' &&
                        (pexelsImagesData?.photos?.length ? (
                          <ImageGallery
                            images={mapPexelsImages(pexelsImagesData.photos)}
                            onImageClick={id => setSelectedItems([id])}
                            selectedImage={selectedItems[0] as string | null}
                            hoveredVideoId={hoveredVideoId}
                            setHoveredVideoId={setHoveredVideoId}
                            onPreviewClick={setPreviewVideoUrl}
                            onImagePreviewClick={setPreviewImageUrl}
                            videoOrientation={videoOrientation}
                          />
                        ) : (
                          <EmptyState
                            message='No images found'
                            icon={
                              <ImageIcon className='h-10 w-10 text-muted-foreground/60' />
                            }
                          />
                        ))}

                      {imageSource === 'pixabay' &&
                        (pixabayImagesData?.hits?.length ? (
                          <ImageGallery
                            images={mapPixabayImages(pixabayImagesData.hits)}
                            onImageClick={id => setSelectedItems([id])}
                            selectedImage={selectedItems[0] as string | null}
                            hoveredVideoId={hoveredVideoId}
                            setHoveredVideoId={setHoveredVideoId}
                            onPreviewClick={setPreviewVideoUrl}
                            onImagePreviewClick={setPreviewImageUrl}
                            videoOrientation={videoOrientation}
                          />
                        ) : (
                          <EmptyState
                            message='No images found'
                            icon={
                              <ImageIcon className='h-10 w-10 text-muted-foreground/60' />
                            }
                          />
                        ))}

                      {imageSource === 'unsplash' &&
                        (unsplashImagesData?.results?.length ? (
                          <ImageGallery
                            images={mapUnsplashImages(
                              unsplashImagesData.results
                            )}
                            onImageClick={id => setSelectedItems([id])}
                            selectedImage={selectedItems[0] as string | null}
                            hoveredVideoId={hoveredVideoId}
                            setHoveredVideoId={setHoveredVideoId}
                            onPreviewClick={setPreviewVideoUrl}
                            onImagePreviewClick={setPreviewImageUrl}
                            videoOrientation={videoOrientation}
                          />
                        ) : (
                          <EmptyState
                            message='No images found'
                            icon={
                              <ImageIcon className='h-10 w-10 text-muted-foreground/60' />
                            }
                          />
                        ))}
                    </>
                  )}

                  {/* GIF Results */}
                  {mediaType === 'gif' && (
                    <>
                      {gifSource === 'giphy' &&
                        (giphyGifsData?.data?.length ? (
                          <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4'>
                            {giphyGifsData.data.map((gif: GiphyGif) => (
                              <div
                                key={gif.id}
                                className={`group relative rounded-lg overflow-hidden cursor-pointer border transition-all duration-200 ${
                                  selectedItems.includes(gif.id)
                                    ? 'border-primary ring-2 ring-primary/30'
                                    : 'border-border hover:border-primary/40'
                                }`}
                                onClick={() => setSelectedItems([gif.id])}
                                style={{
                                  aspectRatio:
                                    videoOrientation === 'portrait'
                                      ? '9/16'
                                      : videoOrientation === 'square'
                                        ? '1/1'
                                        : '16/9',
                                  background: 'var(--muted)',
                                }}
                              >
                                <img
                                  src={gif.images.fixed_height.url}
                                  alt={gif.title}
                                  className='w-full h-full object-cover'
                                />

                                {/* Preview eye icon */}
                                <div className='absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity'>
                                  <div
                                    className='bg-black/70 text-white rounded-full p-1.5 hover:bg-black/90 transition-colors cursor-pointer'
                                    onClick={e => {
                                      e.stopPropagation()
                                      setPreviewImageUrl(
                                        gif.images.original.url
                                      )
                                    }}
                                  >
                                    <Eye className='h-4 w-4' />
                                  </div>
                                </div>

                                {selectedItems.includes(gif.id) && (
                                  <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1'>
                                    <Check className='h-3 w-3' />
                                  </div>
                                )}
                                <div className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2'>
                                  <p className='text-white text-xs truncate'>
                                    {gif.user?.display_name ||
                                      gif.user?.username ||
                                      'Giphy'}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <EmptyState
                            message='No GIFs found'
                            icon={
                              <ImageIcon className='h-10 w-10 text-muted-foreground/60' />
                            }
                          />
                        ))}

                      {gifSource === 'tenor' &&
                        (tenorGifsData?.results?.length ? (
                          <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4'>
                            {tenorGifsData.results.map((gif: TenorGif) => (
                              <div
                                key={gif.id}
                                className={`group relative rounded-lg overflow-hidden cursor-pointer border transition-all duration-200 ${
                                  selectedItems.includes(gif.id)
                                    ? 'border-primary ring-2 ring-primary/30'
                                    : 'border-border hover:border-primary/40'
                                }`}
                                onClick={() => setSelectedItems([gif.id])}
                                style={{
                                  aspectRatio:
                                    videoOrientation === 'portrait'
                                      ? '9/16'
                                      : videoOrientation === 'square'
                                        ? '1/1'
                                        : '16/9',
                                  background: 'var(--muted)',
                                }}
                              >
                                <img
                                  src={
                                    gif.media_formats?.tinygif?.url ||
                                    gif.media_formats?.gif?.url
                                  }
                                  alt={gif.title}
                                  className='w-full h-full object-cover'
                                />

                                {/* Preview eye icon */}
                                <div className='absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity'>
                                  <div
                                    className='bg-black/70 text-white rounded-full p-1.5 hover:bg-black/90 transition-colors cursor-pointer'
                                    onClick={e => {
                                      e.stopPropagation()
                                      setPreviewImageUrl(
                                        gif.media_formats?.gif?.url ||
                                          gif.media_formats?.tinygif?.url ||
                                          ''
                                      )
                                    }}
                                  >
                                    <Eye className='h-4 w-4' />
                                  </div>
                                </div>

                                {selectedItems.includes(gif.id) && (
                                  <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1'>
                                    <Check className='h-3 w-3' />
                                  </div>
                                )}
                                <div className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2'>
                                  <p className='text-white text-xs truncate'>
                                    Tenor
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <EmptyState
                            message='No GIFs found'
                            icon={
                              <ImageIcon className='h-10 w-10 text-muted-foreground/60' />
                            }
                          />
                        ))}
                    </>
                  )}
                </>
              )}
            </div>
          </>
        )}

        {/* My Media Tab */}
        {activeTab === 'my' && (
          <div className='px-6 py-2 h-full flex flex-col'>
            <Tabs
              value={myMediaTab}
              onValueChange={value =>
                setMyMediaTab(value as 'upload' | 'gallery')
              }
              className='flex flex-col h-full'
            >
              <TabsList className='grid w-sm grid-cols-2 mb-2'>
                <TabsTrigger
                  className='dark:data-[state=active]:bg-background dark:data-[state=active]:text-foreground dark:data-[state=active]:shadow-sm'
                  value='upload'
                >
                  Upload Media
                </TabsTrigger>
                <TabsTrigger
                  className='dark:data-[state=active]:bg-background dark:data-[state=active]:text-foreground dark:data-[state=active]:shadow-sm'
                  value='gallery'
                >
                  Media Gallery
                </TabsTrigger>
              </TabsList>

              <TabsContent
                value='upload'
                className='flex-1 flex flex-col overflow-hidden'
              >
                <div className='flex-1 overflow-y-auto px-1'>
                  <div className='space-y-6 pb-4'>
                    <MediaUpload
                      onUploadComplete={handleUploadComplete}
                      onUploadError={handleUploadError}
                      acceptedTypes={['image/*', 'video/*']}
                      maxFileSize={500 * 1024 * 1024} // 500MB
                      multiple={true}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value='gallery' className='flex-1 flex flex-col'>
                <div className='space-y-4 flex-1 flex flex-col'>
                  <div className='flex-1 flex flex-col min-h-0'>
                    <MediaGallery
                      onMediaSelect={handleMediaSelect}
                      className='h-full'
                      searchQuery={myMediaSearch}
                      mediaTypeFilter={myMediaType}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Generate Tab */}
        {activeTab === 'generate' && (
          <div className='p-6 space-y-6'>
            <div className='space-y-2'>
              <label className='text-base font-medium'>
                AI Image Generator
              </label>
              <div className='flex gap-2'>
                <input
                  type='text'
                  className='flex-1 h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                  placeholder='Enter instructions (eg: A lighthouse on a cliff)'
                  value={generatePrompt}
                  onChange={e => setGeneratePrompt(e.target.value)}
                />
                <Button
                  className='whitespace-nowrap'
                  onClick={handleGenerateImage}
                  disabled={generateLoading || !generatePrompt.trim()}
                >
                  {generateLoading ? 'Generating...' : 'Generate'}
                </Button>
              </div>
            </div>

            {/* Model, Orientation, Type/Style side by side */}
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='space-y-2'>
                <Label>Model</Label>
                <select
                  className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                  value={generateModel}
                  onChange={e => setGenerateModel(e.target.value)}
                >
                  {getImgModels.map(m => (
                    <option key={m.value} value={m.value}>
                      {m.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className='space-y-2'>
                <Label>Orientation</Label>
                <select
                  className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                  value={generateOrientation}
                  onChange={e => setGenerateOrientation(e.target.value)}
                >
                  {orientations.map(o => (
                    <option key={o.value} value={o.value}>
                      {o.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className='space-y-2'>
                <Label>Type / Style</Label>
                <select
                  className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                  value={generateStyle}
                  onChange={e => setGenerateStyle(e.target.value)}
                >
                  {styleTypes.map(s => (
                    <option key={s.value} value={s.value}>
                      {s.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {generateError && (
              <div className='text-destructive text-sm'>
                {generateError.message}
              </div>
            )}

            {generatedImage && (
              <div className='flex flex-col items-center gap-2'>
                <img
                  src={generatedImage}
                  alt='Generated'
                  className='rounded-lg max-w-full max-h-80 border cursor-pointer'
                  onClick={() => setSelectedItems([`generated-${Date.now()}`])}
                />
                <Button
                  className='mt-2'
                  onClick={() => {
                    onSelect(
                      {
                        id: `generated-${Date.now()}`,
                        type: 'image',
                        title: 'Generated Image',
                        url: generatedImage,
                        thumbnail: generatedImage,
                      },
                      applyToAll
                    )
                    onClose()
                  }}
                >
                  Use this image
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Imported from Blog Tab */}
        {activeTab === 'blog' &&
          project?.blogImages &&
          project.blogImages.length > 0 && (
            <div className='p-4'>
              <div className='mb-4'>
                <p className='text-xs text-muted-foreground/80'>
                  Images extracted from your content
                </p>
              </div>
              <MasonryGrid
                images={project.blogImages}
                onImageClick={(imageUrl: string) =>
                  setSelectedItems([imageUrl])
                }
                selectedItems={selectedItems}
                onImagePreviewClick={setPreviewImageUrl}
              />
            </div>
          )}
      </div>

      {/* Fixed Pagination - positioned above footer */}
      {renderPagination()}

      {/* Footer with buttons */}
      <div className='border-t border-border p-4 flex items-center justify-between bg-background flex-shrink-0'>
        <div className='flex items-center'></div>
        <div className='flex gap-2'>
          <Button variant='outline' onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSelect} disabled={selectedItems.length === 0}>
            Select
          </Button>
        </div>
      </div>
    </div>
  )

  // Responsive rendering with error boundary
  const renderModal = () => {
    try {
      return isMobile ? (
        <Drawer open={open} onOpenChange={onClose} direction='bottom'>
          <DrawerContent className='max-h-[94vh] h-[94vh] flex flex-col'>
            <DrawerHeader className='px-3 py-2 border-b border-border flex-shrink-0'>
              <DrawerTitle className='text-foreground'>
                Media Picker
              </DrawerTitle>
            </DrawerHeader>
            <div className='flex-1 flex flex-col overflow-hidden min-h-0'>
              {renderContent()}
            </div>
          </DrawerContent>
        </Drawer>
      ) : (
        <Dialog open={open} onOpenChange={onClose}>
          <DialogContent className='bg-background rounded-lg shadow-lg border border-border w-[95%] min-w-[300px] sm:min-w-[600px] md:min-w-[900px] h-[90vh] p-0 max-h-[90vh] overflow-hidden'>
            <div className='flex flex-col h-full'>
              <DialogTitle asChild>
                <div className='p-2 sm:p-3 border-b border-border flex-shrink-0'>
                  <h2 className='text-lg sm:text-xl font-semibold text-foreground'>
                    Media Picker
                  </h2>
                </div>
              </DialogTitle>
              <div className='flex-1 flex flex-col overflow-hidden min-h-0'>
                {renderContent()}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )
    } catch (error) {
      console.error('Error rendering media picker modal:', error)
      return (
        <Dialog open={open} onOpenChange={onClose}>
          <DialogContent className='bg-background rounded-lg shadow-lg border border-border w-[95%] min-w-[300px] sm:min-w-[600px] md:min-w-[900px] h-[80%] p-0 max-h-[80%] overflow-hidden'>
            <div className='flex items-center justify-center h-full'>
              <p className='text-red-500'>
                Error loading media picker. Please try again.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )
    }
  }

  return (
    <>
      {renderModal()}

      {/* Image Preview Dialog */}
      <Dialog
        open={!!previewImageUrl}
        onOpenChange={() => setPreviewImageUrl(null)}
      >
        <DialogContent className='bg-black/90 rounded-lg shadow-lg border-0 w-full max-w-2xl flex flex-col items-center justify-center'>
          <img
            src={previewImageUrl || ''}
            className='w-full h-auto max-h-[70vh] rounded-lg'
            alt='Preview'
            style={{ background: '#000' }}
          />
        </DialogContent>
      </Dialog>

      {/* Video Preview Dialog */}
      <Dialog
        open={!!previewVideoUrl}
        onOpenChange={() => setPreviewVideoUrl(null)}
      >
        <DialogContent className='bg-transparent rounded-lg shadow-lg border-0 w-full max-w-2xl flex flex-col items-center justify-center'>
          {previewVideoUrl && (
            <video
              src={previewVideoUrl}
              className='w-full h-auto max-h-[70vh] rounded-lg'
              autoPlay
              muted
              loop
              controls
              style={{ background: '#000' }}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
