'use client'

import React, { useState, useMemo, useRef, useEffect, useCallback } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'

import { Progress } from '@/components/ui/progress'
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'
import { useMediaQuery } from '@/hooks/use-media-query'
import {
  Play,
  Pause,
  Music2,
  Clock,
  User,
  Upload,
  Loader2,
  Image as ImageIcon,
  Check,
} from 'lucide-react'
import { useMusicQuery } from '@/hooks/useMusicQuery'
import { useUserAudioQuery } from '@/hooks/useUserAudioQuery'
import { useAudioUpload } from '@/hooks/useAudioUpload'
import { useQueryClient } from '@tanstack/react-query'
import { MusicTrack } from '@/app/api/music/route'
import { cn } from '@/lib/utils'
import { toast } from '@/lib/toast'

interface MusicPickerModalProps {
  isOpen: boolean
  onClose: () => void
  onSelectMusic: (music: MusicTrack) => void
}

// Isolated Track Card Component - Completely independent, no external state dependencies
const IsolatedTrackCard = ({
  track,
  onSelect,
  initialSelected = false,
}: {
  track: MusicTrack
  onSelect: (track: MusicTrack) => void
  initialSelected?: boolean
}) => {
  // Internal state only - no external dependencies
  const [isSelected, setIsSelected] = useState(initialSelected)
  const [isPlaying, setIsPlaying] = useState(false)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  const handleSelect = useCallback(() => {
    setIsSelected(true)
    onSelect(track)
  }, [track, onSelect])

  const handlePlay = useCallback(async () => {
    if (isPlaying) {
      // Pause current audio
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current = null
      }
      setIsPlaying(false)
      return
    }

    if (track.previewUrl) {
      setIsPlaying(true)
      const audio = new Audio(track.previewUrl)
      audioRef.current = audio

      audio.onended = () => {
        setIsPlaying(false)
        audioRef.current = null
      }
      audio.onerror = () => {
        setIsPlaying(false)
        audioRef.current = null
      }

      try {
        await audio.play()
      } catch (error) {
        console.error('Error playing track preview:', error)
        setIsPlaying(false)
        audioRef.current = null
      }
    }
  }, [track, isPlaying])

  return (
    <Card
      className={`
        track-card-isolated relative cursor-pointer transition-all duration-200
        hover:shadow-md hover:border-primary/30
        ${isSelected ? 'border-primary bg-primary/5 shadow-md' : 'border-border shadow-sm'}
        ${isSelected ? 'hover:bg-primary/10 hover:border-primary/50' : ''}
      `}
      onClick={handleSelect}
    >
      {/* Checkmark overlay when selected */}
      {isSelected && (
        <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1 z-10'>
          <Check className='h-3 w-3' />
        </div>
      )}

      <CardContent className='p-4 pb-4'>
        {/* Track Header with Play Button */}
        <div className='flex items-center gap-3 mb-3'>
          <div className='relative'>
            <div className='w-12 h-12 rounded-full bg-muted/40 flex items-center justify-center'>
              <Button
                size='sm'
                variant='ghost'
                className='h-8 w-8 p-0 rounded-full hover:bg-muted'
                onClick={e => {
                  e.stopPropagation()
                  handlePlay()
                }}
              >
                {isPlaying ? (
                  <Pause className='h-4 w-4' />
                ) : (
                  <Play className='h-4 w-4 ml-0.5' />
                )}
              </Button>
            </div>
          </div>

          <div className='flex-1 min-w-0'>
            <div className='flex items-center gap-2 mb-1'>
              <h3 className='font-semibold text-base truncate'>
                {track.title}
              </h3>
              <div className='flex items-center gap-1 text-muted-foreground'>
                <Clock className='h-3 w-3' />
                <span className='text-xs'>
                  {Math.floor(track.durationMillis / 60000)}:
                  {Math.floor((track.durationMillis % 60000) / 1000)
                    .toString()
                    .padStart(2, '0')}
                </span>
              </div>
            </div>
            <div className='flex items-center gap-1 text-sm text-muted-foreground'>
              <User className='h-3 w-3' />
              <span className='truncate'>{track.artistName}</span>
            </div>
          </div>
        </div>

        {/* Track Tags/Pills */}
        <div className='flex flex-wrap gap-1.5 mb-0'>
          {/* Genre Badge */}
          {track.genre && (
            <Badge
              variant='secondary'
              className='text-xs px-2 py-0.5 capitalize'
            >
              {track.genre}
            </Badge>
          )}

          {/* Mood Badge */}
          {track.mood && (
            <Badge variant='outline' className='text-xs px-2 py-0.5 capitalize'>
              {track.mood}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function MusicPickerModal({
  isOpen,
  onClose,
  onSelectMusic,
}: MusicPickerModalProps) {
  const [activeTab, setActiveTab] = useState('stock')
  const [tempSelectedTrack, setTempSelectedTrack] = useState<MusicTrack | null>(
    null
  )
  const [currentPage, setCurrentPage] = useState(1)
  const isDesktop = useMediaQuery('(min-width: 768px)')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const limit = 10

  const { uploadMultipleAudio, batchProgress, isUploading } = useAudioUpload()
  const queryClient = useQueryClient()

  // Fetch music data with pagination
  const { data: musicData, isLoading } = useMusicQuery({
    page: currentPage,
    limit,
  })

  // Fetch user uploaded audio
  const { data: userAudioData, isLoading: isLoadingUserAudio } =
    useUserAudioQuery({
      page: 1,
      limit: 50, // Show more uploaded tracks
    })

  // Reset modal state when closed
  useEffect(() => {
    if (!isOpen) {
      setTempSelectedTrack(null)
      setCurrentPage(1)
      setActiveTab('stock')
    }
  }, [isOpen])

  // Use tracks directly from API
  const tracks = useMemo(() => {
    return musicData?.data || []
  }, [musicData?.data])

  // Stable callback for track selection
  const handleTrackSelect = useCallback((track: MusicTrack) => {
    setTempSelectedTrack(track)
  }, [])

  const handleConfirmSelection = () => {
    if (tempSelectedTrack) {
      onSelectMusic(tempSelectedTrack)
      onClose()
    }
  }

  const handleCancel = () => {
    setTempSelectedTrack(null)
    onClose()
  }

  const handleFileUpload = useCallback(
    async (files: File[]) => {
      if (isUploading) {
        toast.error('Upload already in progress')
        return
      }

      try {
        // Filter audio files
        const audioFiles = files.filter(file => file.type.startsWith('audio/'))

        if (audioFiles.length === 0) {
          toast.error('No valid audio files found')
          return
        }

        // Upload files using the audio upload logic
        const results = await uploadMultipleAudio(audioFiles)

        // Process uploaded files - they will be fetched from the API on next query
        console.log(`Successfully uploaded ${results.length} audio file(s)`)

        // Invalidate user audio query to refresh the list
        await queryClient.invalidateQueries({ queryKey: ['user-audio'] })

        toast.success(
          `Successfully uploaded ${results.length} file${results.length !== 1 ? 's' : ''}!`
        )
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Upload failed'
        toast.error(errorMessage)
      }
    },
    [uploadMultipleAudio, isUploading, queryClient]
  )

  const handleFileInputChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files
    if (files) {
      handleFileUpload(Array.from(files))
    }
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    const files = event.dataTransfer.files
    if (files.length > 0) {
      handleFileUpload(Array.from(files))
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
  }

  const UploadBox = () => (
    <div className='mb-4'>
      <div
        className={cn(
          'border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer transition-colors',
          'hover:border-primary/50 hover:bg-muted/20',
          isUploading && 'pointer-events-none opacity-75'
        )}
        onClick={!isUploading ? handleUploadClick : undefined}
        onDrop={!isUploading ? handleDrop : undefined}
        onDragOver={handleDragOver}
      >
        {isUploading ? (
          <div className='flex flex-col items-center'>
            <Loader2 className='h-8 w-8 mx-auto mb-2 animate-spin text-primary' />
            <p className='text-sm font-medium mb-1'>Uploading...</p>
            {batchProgress && (
              <div className='w-full mt-3'>
                <div className='flex items-center justify-between text-xs mb-1'>
                  <span>Progress</span>
                  <span>{batchProgress.overallProgress}%</span>
                </div>
                <Progress
                  value={batchProgress.overallProgress}
                  className='h-2'
                />
                <p className='text-xs text-muted-foreground mt-1'>
                  {batchProgress.currentFile}
                </p>
              </div>
            )}
          </div>
        ) : (
          <>
            <Upload className='h-8 w-8 mx-auto mb-2 text-muted-foreground' />
            <p className='text-sm font-medium mb-1'>
              Drop files here or click to browse
            </p>
            <p className='text-xs text-muted-foreground'>
              Supports MP3 and WAV
            </p>
          </>
        )}
      </div>
      <input
        ref={fileInputRef}
        type='file'
        accept='audio/mp3,audio/wav,audio/*'
        multiple
        onChange={handleFileInputChange}
        className='hidden'
      />
    </div>
  )

  // Pagination component - extracted for reuse
  const PaginationComponent = () => {
    if (!musicData || isLoading || tracks.length === 0) return null

    return (
      <div className='flex items-center justify-center py-3 bg-background border-t'>
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href='#'
                onClick={e => {
                  e.preventDefault()
                  if (currentPage > 1) {
                    setCurrentPage(currentPage - 1)
                  }
                }}
                className={
                  currentPage === 1 ? 'pointer-events-none opacity-50' : ''
                }
              />
            </PaginationItem>

            {/* Page Numbers */}
            {(() => {
              const totalPages = Math.ceil(musicData.count / limit)
              const pages = []
              const maxVisiblePages = 5

              let startPage = Math.max(
                1,
                currentPage - Math.floor(maxVisiblePages / 2)
              )
              const endPage = Math.min(
                totalPages,
                startPage + maxVisiblePages - 1
              )

              // Adjust start if we're near the end
              if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1)
              }

              // First page + ellipsis
              if (startPage > 1) {
                pages.push(
                  <PaginationItem key={1}>
                    <PaginationLink
                      href='#'
                      onClick={e => {
                        e.preventDefault()
                        setCurrentPage(1)
                      }}
                      isActive={currentPage === 1}
                    >
                      1
                    </PaginationLink>
                  </PaginationItem>
                )
                if (startPage > 2) {
                  pages.push(
                    <PaginationItem key='ellipsis-start'>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )
                }
              }

              // Visible page range
              for (let i = startPage; i <= endPage; i++) {
                pages.push(
                  <PaginationItem key={i}>
                    <PaginationLink
                      href='#'
                      onClick={e => {
                        e.preventDefault()
                        setCurrentPage(i)
                      }}
                      isActive={currentPage === i}
                    >
                      {i}
                    </PaginationLink>
                  </PaginationItem>
                )
              }

              // Ellipsis + last page
              if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                  pages.push(
                    <PaginationItem key='ellipsis-end'>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )
                }
                pages.push(
                  <PaginationItem key={totalPages}>
                    <PaginationLink
                      href='#'
                      onClick={e => {
                        e.preventDefault()
                        setCurrentPage(totalPages)
                      }}
                      isActive={currentPage === totalPages}
                    >
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                )
              }

              return pages
            })()}

            <PaginationItem>
              <PaginationNext
                href='#'
                onClick={e => {
                  e.preventDefault()
                  const totalPages = Math.ceil(musicData.count / limit)
                  if (currentPage < totalPages) {
                    setCurrentPage(currentPage + 1)
                  }
                }}
                className={
                  currentPage >= Math.ceil(musicData.count / limit)
                    ? 'pointer-events-none opacity-50'
                    : ''
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    )
  }

  // Replace TabsContent with div in StockTab
  const StockTab = () => (
    <div className='h-full flex flex-col'>
      <TrackList tracks={tracks} isLoading={isLoading} />
    </div>
  )

  // Replace TabsContent with div in MyMediaTab
  const MyMediaTab = () => (
    <div className='space-y-4'>
      <UploadBox />
      <TrackList
        tracks={userAudioData?.data || []}
        isLoading={isLoadingUserAudio}
      />
    </div>
  )

  const TrackList = ({
    tracks,
    isLoading,
  }: {
    tracks: MusicTrack[]
    isLoading: boolean
  }) => (
    <div className='space-y-4'>
      {/* Track Grid */}
      <div className='overflow-x-hidden'>
        {isLoading ? (
          <div className='flex items-center justify-center py-8'>
            <div className='text-center'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2' />
              <p className='text-sm text-muted-foreground'>Loading music...</p>
            </div>
          </div>
        ) : tracks.length === 0 ? (
          <div className='flex items-center justify-center py-8'>
            <div className='text-center'>
              <Music2 className='h-8 w-8 text-muted-foreground mx-auto mb-2' />
              <p className='text-sm font-medium mb-2'>No tracks found</p>
              <p className='text-xs text-muted-foreground mb-4'>
                {activeTab === 'stock'
                  ? 'No music tracks available'
                  : 'Upload some music to get started'}
              </p>
            </div>
          </div>
        ) : (
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4 pb-4'>
            {tracks.map(track => (
              <IsolatedTrackCard
                key={track.id}
                track={track}
                onSelect={handleTrackSelect}
                initialSelected={tempSelectedTrack?.id === track.id}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )

  // Add TabButton component
  function TabButton({
    active,
    onClick,
    icon,
    label,
  }: {
    active: boolean
    onClick: () => void
    icon?: React.ReactNode
    label: string
  }) {
    return (
      <button
        type='button'
        onClick={onClick}
        className={cn(
          'flex items-center gap-2 px-4 py-2 font-medium border-b-2 transition-colors',
          active
            ? 'border-primary text-primary'
            : 'border-transparent text-muted-foreground hover:text-primary'
        )}
      >
        {icon}
        {label}
      </button>
    )
  }

  if (isDesktop) {
    return (
      <Dialog open={isOpen} onOpenChange={handleCancel}>
        <DialogContent className='min-w-4xl max-h-[85vh] h-[85vh] flex flex-col p-0'>
          <DialogHeader className='px-6 py-4 border-b'>
            <DialogTitle className='text-xl font-semibold'>
              Choose Background Music
            </DialogTitle>
          </DialogHeader>

          {/* Tab navigation */}
          <div className='border-b border-border flex pl-4'>
            <div className='flex'>
              <TabButton
                active={activeTab === 'stock'}
                onClick={() => setActiveTab('stock')}
                icon={null}
                label='Stock'
              />
              <TabButton
                active={activeTab === 'my-media'}
                onClick={() => setActiveTab('my-media')}
                icon={<ImageIcon className='h-4 w-4' />}
                label='My Media'
              />
            </div>
          </div>

          {/* Main Content: Tabs */}
          <div className='flex-1 min-h-0 overflow-hidden flex flex-col'>
            <div className='flex-1 overflow-y-auto px-4 py-4'>
              {activeTab === 'stock' && <StockTab />}
              {activeTab === 'my-media' && <MyMediaTab />}
            </div>

            {/* Fixed Pagination - only show for stock tab */}
            {activeTab === 'stock' && <PaginationComponent />}
          </div>

          {/* Footer Buttons */}
          <div className='flex items-center justify-end gap-3 px-6 py-4 border-t bg-muted/20'>
            <Button variant='outline' onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmSelection}
              disabled={!tempSelectedTrack}
              className='bg-primary hover:bg-primary/90'
            >
              Confirm Selection
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Drawer open={isOpen} onOpenChange={handleCancel}>
      <DrawerContent className='max-h-[85vh]'>
        <DrawerHeader className='border-b'>
          <DrawerTitle className='flex items-center gap-2'>
            <Music2 className='h-5 w-5' />
            Choose Background Music
          </DrawerTitle>
        </DrawerHeader>

        <div className='flex-1 flex flex-col overflow-hidden'>
          {/* Tab navigation - using same TabButton components as desktop */}
          <div className='border-b border-border flex pl-4'>
            <div className='flex'>
              <TabButton
                active={activeTab === 'stock'}
                onClick={() => setActiveTab('stock')}
                icon={null}
                label='Stock'
              />
              <TabButton
                active={activeTab === 'my-media'}
                onClick={() => setActiveTab('my-media')}
                icon={<ImageIcon className='h-4 w-4' />}
                label='My Media'
              />
            </div>
          </div>

          <div className='flex-1 overflow-y-auto px-4 py-4'>
            {activeTab === 'stock' && <StockTab />}
            {activeTab === 'my-media' && <MyMediaTab />}
          </div>

          {/* Fixed Pagination - only show for stock tab */}
          {activeTab === 'stock' && <PaginationComponent />}
        </div>

        {/* Mobile Footer */}
        <div className='flex items-center justify-end gap-3 px-4 py-4 border-t bg-muted/20'>
          <Button variant='outline' onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmSelection}
            disabled={!tempSelectedTrack}
            className='bg-primary hover:bg-primary/90'
          >
            Confirm Selection
          </Button>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
