import { NextRequest } from 'next/server'
import crypto from 'crypto'
import {
  createCachedResponse,
  createErrorResponse,
  CACHE_DURATIONS,
} from '@/lib/api-cache'

const PODCAST_INDEX_API_KEY = process.env.PODCAST_INDEX_API_KEY
const PODCAST_INDEX_API_SECRET = process.env.PODCAST_INDEX_API_SECRET
const PODCAST_INDEX_BASE_URL = 'https://api.podcastindex.org/api/1.0'

interface PodcastSearchResult {
  id: number
  title: string
  url: string
  originalUrl: string
  link: string
  description: string
  author: string
  ownerName: string
  image: string
  artwork: string
  lastUpdateTime: number
  lastCrawlTime: number
  lastParseTime: number
  inPollingQueue: number
  priority: number
  language: string
  generator: string
  type: number
  dead: number
  chash: string
  episodeCount: number
  crawlErrors: number
  parseErrors: number
  categories: Record<string, string>
  locked: number
  explicit: boolean
  podcastGuid: string
  medium: string
  episodeGuid: string
  imageUrlHash: number
  newestItemPubdate: number
}

interface PodcastIndexSearchResponse {
  status: string
  feeds: PodcastSearchResult[]
  count: number
  query: string
  description: string
}

function generateAuthHeaders() {
  if (!PODCAST_INDEX_API_KEY || !PODCAST_INDEX_API_SECRET) {
    console.error('Missing Podcast Index API credentials')
    throw new Error('Podcast Index API credentials not configured')
  }

  const apiHeaderTime = Math.floor(Date.now() / 1000)
  const hashString =
    PODCAST_INDEX_API_KEY + PODCAST_INDEX_API_SECRET + apiHeaderTime.toString()
  const hash = crypto
    .createHash('sha1')
    .update(hashString, 'utf8')
    .digest('hex')

  return {
    'X-Auth-Date': apiHeaderTime.toString(),
    'X-Auth-Key': PODCAST_INDEX_API_KEY,
    Authorization: hash,
    'User-Agent': 'AdoriAI/1.0',
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const max = searchParams.get('max') || '20'
    const clean = searchParams.get('clean') || 'true'

    if (!query || query.trim().length === 0) {
      return createErrorResponse('Search query is required', 400)
    }

    const headers = generateAuthHeaders()
    const searchUrl = `${PODCAST_INDEX_BASE_URL}/search/byterm?q=${encodeURIComponent(query)}&max=${max}&clean=${clean}`

    const response = await fetch(searchUrl, {
      method: 'GET',
      headers,
    })

    if (!response.ok) {
      const responseText = await response.text()
      console.error('Podcast Index API error:', {
        status: response.status,
        statusText: response.statusText,
        body: responseText,
      })

      return createErrorResponse(
        `Failed to search podcasts: ${response.status} ${response.statusText}`,
        response.status
      )
    }

    const data: PodcastIndexSearchResponse = await response.json()

    // Transform the response to match our frontend needs
    const transformedFeeds = data.feeds.map(feed => ({
      id: feed.id,
      title: feed.title,
      author: feed.author || feed.ownerName,
      description: feed.description,
      image: feed.image || feed.artwork,
      url: feed.url,
      link: feed.link,
      episodeCount: feed.episodeCount,
      language: feed.language,
      explicit: feed.explicit,
      lastUpdateTime: feed.lastUpdateTime,
      categories: feed.categories,
    }))

    return createCachedResponse(
      {
        status: data.status,
        podcasts: transformedFeeds,
        count: data.count,
        query: data.query,
      },
      CACHE_DURATIONS.PODCAST_SEARCH
    )
  } catch (error) {
    console.error('Podcast search error:', error)
    return createErrorResponse('Internal server error', 500)
  }
}
