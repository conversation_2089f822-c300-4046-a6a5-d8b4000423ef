'use client'

import React from 'react'
import { useIsFetching } from '@tanstack/react-query'
import { authClient } from '@/lib/auth-client'
import { FullscreenGlassLoader } from '@/components/ui/fullscreen-glass-loader'

/**
 * Tracks critical startup readiness and shows a full-screen loader until ready.
 *
 * Critical queries considered:
 * - Session (auth)
 * - Active organization
 * - Subscriptions for the active reference
 * - Fonts (used in many places)
 * - Background prefetch batch that QueryPrefetcher runs
 */
export function StartupLoaderProvider({
  children,
}: {
  children: React.ReactNode
}) {
  // Auth readiness
  const { isPending: sessionLoading } = authClient.useSession()
  const { isPending: orgLoading } = authClient.useActiveOrganization()

  // Track a group of important queries by keys.
  // useIsFetching returns a count of matching observers that are fetching.
  const fetchingSubscriptions = useIsFetching({ queryKey: ['subscriptions'] })
  const fetchingFonts = useIsFetching({ queryKey: ['fonts'] })
  const fetchingProjects = useIsFetching({ queryKey: ['projects'] })
  const fetchingVoices = useIsFetching({ queryKey: ['eleven-voices'] })
  const fetchingYoutube = useIsFetching({ queryKey: ['youtube-connections'] })

  const isInitializing =
    sessionLoading ||
    orgLoading ||
    fetchingSubscriptions > 0 ||
    fetchingFonts > 0 ||
    fetchingProjects > 0 ||
    fetchingVoices > 0 ||
    fetchingYoutube > 0

  // Show loader only during the first app boot and never again.
  const [hasMounted, setHasMounted] = React.useState(false)
  const [startupDone, setStartupDone] = React.useState(false)

  React.useEffect(() => setHasMounted(true), [])

  // When initialization finishes the first time, mark startup as done.
  React.useEffect(() => {
    if (!startupDone && hasMounted && !isInitializing) {
      setStartupDone(true)
    }
  }, [startupDone, hasMounted, isInitializing])

  if (!hasMounted) return null

  const shouldShow = !startupDone && isInitializing

  return (
    <>
      {shouldShow && (
        <FullscreenGlassLoader message='Loading your workspace…' />
      )}
      {children}
    </>
  )
}
