'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { StartupLoaderProvider } from '@/providers/startup-loader-provider'

export function AuthAwareStartup({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const isAuthRoute = React.useMemo(() => {
    if (!pathname) return false
    return (
      pathname.startsWith('/signin') ||
      pathname.startsWith('/signup') ||
      pathname.startsWith('/forgot-password') ||
      pathname.startsWith('/reset-password')
    )
  }, [pathname])

  if (isAuthRoute) {
    return <>{children}</>
  }

  return <StartupLoaderProvider>{children}</StartupLoaderProvider>
}
